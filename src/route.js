import VueRouter from 'vue-router';
import WinterSpringPrimaryLessonIntro2024 from './modules/winterSpringPrimaryLessonIntro-2024/index.vue';
import Teacher from './modules/winterSpringPrimaryLessonIntro-2024/components/teacher.vue';
import Table from './components/table.vue';
import Tabs from './components/tabs.vue';
import Swiper from './components/swiper.vue';
import BookCard from './components/book-card.vue';

const routes = [
    // 课程详情 - 老师介绍页
    { path: '/teacher', component: Teacher },
    // 调试基地原生表格路由
    { path: '/table', component: Table },
    // 调试基地原生tabs路由
    { path: '/tabs', component: Tabs },
    // 调试基地原生swiper路由
    { path: '/swiper', component: Swiper },
    // 调试 2024 冬春课程详情，组装成了聚合页面展示
    { path: '/lesson-2024', component: WinterSpringPrimaryLessonIntro2024 },
    // BookCard 组件调试路由
    { path: '/book-card', component: BookCard }
];

export default new VueRouter({routes});
