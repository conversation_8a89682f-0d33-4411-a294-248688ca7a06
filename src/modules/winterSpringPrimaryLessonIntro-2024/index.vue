<template>
    <div>
        <Header></Header>
        <Title></Title>
        <ClassStudy></ClassStudy>
        <Title></Title>
        <ClassLoop></ClassLoop>
        <ClassLoop2></ClassLoop2>
        <Title></Title>
        <IntrestClassRoom></IntrestClassRoom>
        <TechAssistment></TechAssistment>
        <FrequencyAsks></FrequencyAsks>
    </div>
</template>

<script>
import Header from './components/header.vue';
import Title from './components/title.vue';
import ClassStudy from './components/classStudy.vue';
import ClassLoop from './components/classLoop.vue';
import ClassLoop2 from './components/classLoop2.vue';
import IntrestClassRoom from './components/intrestClassRoom.vue';
import TechAssistment from './components/techAssistment.vue';
import FrequencyAsks from './components/frequencyAsks.vue';
export default {
    components: {
        Header,
        Title,
        ClassStudy,
        ClassLoop,
        ClassLoop2,
        IntrestClassRoom,
        TechAssistment,
        FrequencyAsks
    }
};
</script>
