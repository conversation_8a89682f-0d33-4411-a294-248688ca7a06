<template>
    <div class="study">
        <div class="inner-wrap">
            <div class="item">
                <div class="left">
                    <div class="img-wrap">
                        <img src="https://mktb.fbcontent.cn/2024/10/7ccc906feae7.png">
                    </div>
                </div>
                <div class="right">
                    <div class="title">
                        <img class="star" src="https://mktb.fbcontent.cn/2024/10/8285e459b64b.png">
                        最多八字最多八字
                    </div>
                    <div class="content">
                        本书由猿辅导教研组精心编撰，
                        <span class="point">历经5年迭代优化。涵盖核心知识点和重点题型</span>
                        科学学习重点内容，建立知识体系。
                    </div>
                </div>
            </div>
            <div class="item">
                <div class="left">
                    <div class="img-wrap">
                        <img src="https://mktb.fbcontent.cn/2024/10/7ccc906feae7.png">
                    </div>
                </div>
                <div class="right">
                    <div class="title">
                        <img class="star" src="https://mktb.fbcontent.cn/2024/10/8285e459b64b.png">
                        最多八字最多八字
                    </div>
                    <div class="content">
                        本书由猿辅导教研组精心编撰，
                        <span class="point">历经5年迭代优化。涵盖核心知识点和重点题型</span>
                        科学学习重点内容，建立知识体系。
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<style lang="less" scoped>
.study {
    background: linear-gradient(to bottom, #FFF7CD 50%, #FFE581 50%);
    padding-bottom: 15px;
}
.inner-wrap {
    width: 100%;
    background: #fff;
    padding: 21px 15px 24px 15px;
    border-radius: 20px;
    box-sizing: border-box;

    .item {
        margin-bottom: 15px;
        background: #FFF5E4;
        border-radius: 12px;
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 7px 15px 7px 7px;

        &:last-of-type {
            margin-bottom: 0;
        }

        .left {
            flex-basis: 122px;
            margin-right: 17px;

            .img-wrap {
                background: #BEBFBE;
                border-radius: 12px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: auto;
                }
            }
        }

        .right {
            width: 50%;
            margin-top: 12px;

            .title {
                width: 131px;
                height: 30px;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient( 180deg, #CEF947 0%, #ACF84E 100%);
                border-radius: 15px;
                border: 2px solid #FFFFFF;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 13px;
                color: #020202;
                line-height: 19px;
                letter-spacing: 1px;
                text-align: left;
                font-style: normal;
                text-transform: uppercase;

                .star {
                    position: absolute;
                    left: -2px;
                    top: -5px;
                    width: 12px;
                }
            }
            .content {
                margin-top: 5px;
                padding-left: 2px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 13px;
                color: #000000;
                line-height: 20px;
                text-align: justify;
                font-style: normal;

                .point {
                    color: #FF6605;
                }
            }
        }
    }

}
</style>
