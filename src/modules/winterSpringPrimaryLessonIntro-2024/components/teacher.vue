<template>
    <div class="teacher">
        <div class="img-wrap">
            <img class="avatar" src="https://mkta.fbcontent.cn/2024/10/92bb0e10050b.png">
        </div>
        <div class="teacher-name">老师</div>
        <div class="teacher-intro">
            <div class="school">
                <span class="text">
                    毕业于不少于十三个的字大学学校
                </span>
            </div>
            <ul class="feature">
                <li>风格幽默亲和课程逻辑性强</li>
                <li>最多十二个字</li>
                <li>互动教学，培养学习能力</li>
            </ul>
        </div>
        <div class="wrap"></div>
    </div>
</template>
<style lang="less" scoped>
.teacher {
    width: 100%;
    height: 241px;
    position: relative;
    background-image: url('https://mktb.fbcontent.cn/2024/10/cecd1d197e0a.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;


    .img-wrap {
        position: absolute;
        left: -60px;
        bottom: 0;
        img {
            width: 310px;
            height: auto;
        }
    }

    .teacher-name {
        position: absolute;
        left: 186px;
        top: 56px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 700;
        font-size: 23px;
        color: #000000;
        line-height: 32px;
        letter-spacing: 5px;
        text-align: left;
        font-style: normal;
    }

    .teacher-intro {
        position: absolute;
        left: 186px;
        top: 76px;

        .school {
            width: 180px;
            height: 39px;
            position: relative;
            left: -6px;
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            background-image: url('https://mktb.fbcontent.cn/2024/10/14f85ad8a05e.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;

            .text {
                margin-top: 15px;
                margin-left: -5px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 10px;
                color: #000000;
                line-height: 15px;
                text-align: left;
                font-style: normal;
            }
        }

        .feature {
            width: 160px;
            position: relative;
            top: -10px;
            box-sizing: border-box;
            padding: 20px 10px 3px 10px;
            background: #FFFFFF;
            border-radius: 6px;
            border: 1px solid #FFFFFF;

            li {
                height: 15px;
                margin-bottom: 4px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 10px;
                color: #2A2928;
                line-height: 14px;
                text-align: left;
                font-style: normal;

                &::before {
                    content: '';
                    display: inline-block;
                    width: 3px;
                    height: 3px;
                    border-radius: 50%;
                    background: #FF5700;
                    margin-right: 4px;
                    margin-bottom: 2px;
                }
            }
        }

    }
}
</style>
