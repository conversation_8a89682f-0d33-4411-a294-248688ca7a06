<template>
    <div class="wrap">
        <h3>
            <span>如何选择版本？</span>
        </h3>
        <h5>目标A班/A+班</h5>
        <div class="content-box">
            <dl>
                <dt>人教版</dt>
                <dd>课内知识掌握较好一行分以上，希望突破重点难啊的味道点题型布置</dd>
            </dl>
            <dl>
                <dt>人教版</dt>
                <dd>课内知识掌握较好一行分以上，希望突破重点难啊的味道点题型布置</dd>
            </dl>
            <dl>
                <dt>人教版</dt>
                <dd>课内知识掌握较好一行分以上，希望突破重点难啊的味道点题型布置</dd>
            </dl>
            <dl>
                <dt>人教版</dt>
                <dd>课内知识掌握较好一行分以上，希望突破重点难啊的味道点题型布置</dd>
            </dl>
        </div>
        <div class="remarks">详细班型信息可联系客服获取事是是无好似是如何设计可好吃还在上学时就是时间会就是觉得</div>
    </div>
</template>
<style lang="less" scoped>
.wrap {
    padding-bottom: 30px;
    > h3 {
        margin-bottom: 20px;
        text-align: center;
        > span {
            display: inline-block;
            padding: 2px 9px 2px 16px;
            background: linear-gradient(90deg, #FF7818 0%, #FF9407 100%);
            height: 24px;
            border-radius: 50px;
            line-height: 24px;
            color: #fff;
            font-size: 14px;
            letter-spacing: 1;
            font-weight: bold;
        }
    }
    > h5 {
        display: inline-block;
        margin-left: 23px;
        padding: 0 13px;
        height: 26px;
        text-align: center;
        line-height: 26px;
        color: #fff;
        background: #FF7818;
        font-size: 12px;
        box-shadow: 0px 2px 5px 0px rgba(255,178,123,1);
        border-radius: 30px 50px 50px 0px;
    }
    > .content-box {
        box-sizing: border-box;
        margin: -13px 23px 0;
        padding-top: 13px;
        background: #FFFDF9;
        box-shadow: 0px 2px 5px 0px rgba(255,231,209,1);
        border-radius: 10px;
        border: 1px solid rgba(255,217,181,0.4);
        > dl {
            display: flex;
            align-items: center;
            margin: 0 12px;
            height: 56px;
            border-bottom: 1px solid rgba(255,217,181,0.4);
            > dt {
                margin-right: 8px;
                width: 54px;
                height: 20px;
                background: #FF9208;
                border-radius: 13px;
                color: #fff;
                line-height: 20px;
                text-align: center;
                font-size: 10px;
                font-weight: bold;
            }
            > dd {
                width: 245px;
                height: 30px;
                font-size: 11px;
                color: #242736;
                line-height: 15px;
                letter-spacing: 1px;
            }
            &:last-of-type {
                border: none;
            }
        }
    }
}
.remarks {
    position: relative;
    padding: 11px 25px 0 32px;
    font-size: 11px;
    font-weight: 300;
    color: #50525D;
    line-height: 15px;
    letter-spacing: 1px;
    &::before {
        content: '';
        position: absolute;
        left: 22px;
        top: 16px;
        width: 4px;
        height: 4px;
        background: #FE4628;
        border-radius: 50%;
    }
}
</style>
