// 基地表格4行2列 数据mock 基地配置的表格数据 ，这里只是示例数据，需要添加行或者列的时候，直接在这里添加即可
export const table4Rows2Columns = [
    {
        "id": "row-1730447876881",
        "className": "row row0",
        "childs": [
            {
                "id": "col-1730447876881-1",
                "className": "col row0-col0",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            },
            {
                "id": "col-1730447876881-2",
                "className": "col row0-col1",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            }
        ]
    },
    {
        "id": "row-1730447876881-3",
        "className": "row row1",
        "childs": [
            {
                "id": "col-1730447876881-4",
                "className": "col row1-col0",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            },
            {
                "id": "col-1730447876881-5",
                "className": "col row1-col1",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            }
        ]
    },
    {
        "id": "row-1730447876881-6",
        "className": "row row2",
        "childs": [
            {
                "id": "col-1730447876881-7",
                "className": "col row2-col0",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            },
            {
                "id": "col-1730447876881-8",
                "className": "col row2-col1",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            }
        ]
    },
    {
        "id": "row-1730447876881-9",
        "className": "row row3",
        "childs": [
            {
                "id": "col-1730447876881-10",
                "className": "col row3-col0",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730447876881-11",
                "className": "col row3-col1",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            }
        ]
    }
];

// 基地表格5行4列 数据mock 基地配置的表格数据 ，这里只是示例数据，需要添加行或者列的时候，直接在这里添加即可
export const tableWith5Rows4Columns = [
    {
        "id": "row-1730452348553",
        "className": "row row0",
        "childs": [
            {
                "id": "col-1730452348553-1",
                "className": "col row0-col0",
                "content": "<p>班型难度</p>",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            },
            {
                "id": "col-1730452348553-2",
                "className": "col row0-col1",
                "content": "<p>文</p>",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": [
                    {
                        "src": "https://mktb.fbcontent.cn/2020/8/be23aceb65f8.svg"
                    },
                    {
                        "src": "https://mktb.fbcontent.cn/2020/8/be23aceb65f8.svg"
                    },
                    {
                        "src": "https://mktb.fbcontent.cn/2020/8/be23aceb65f8.svg"
                    },
                    {
                        "src": "https://mktb.fbcontent.cn/2020/8/be23aceb65f8.svg"
                    }
                ]
            },
            {
                "id": "col-1730452348553-3",
                "className": "col row0-col2",
                "content": "<p>授课节奏</p>",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452348553-4",
                "className": "col row0-col3",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            }
        ]
    },
    {
        "id": "row-1730452352785",
        "className": "row row1",
        "childs": [
            {
                "id": "col-1730452348553-6",
                "className": "col row1-col0",
                "content": "<p>适合学生</p>",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            },
            {
                "id": "col-1730452355470",
                "className": "col row1-col1",
                "content": "<p>得分率70%</p>",
                "colspan": 3,
                "rowspan": 1,
                "style": "",
                "images": []
            },
            {
                "id": "col-1730452355470-1",
                "className": "col row1-col2 hid",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452355470-2",
                "className": "col row1-col3 hid",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            }
        ]
    },
    {
        "id": "row-1730452357387",
        "className": "row row2",
        "childs": [
            {
                "id": "col-1730452348553-11",
                "className": "col row2-col0",
                "content": "<p>学习目标</p>",
                "colspan": 1,
                "rowspan": 1,
                "style": "",
                "images": []
            },
            {
                "id": "col-1730452358689",
                "className": "col row2-col1",
                "content": "<p>希望深度探索，综合训练冲刺难题速解</p>",
                "colspan": 3,
                "rowspan": 1,
                "style": "",
                "images": []
            },
            {
                "id": "col-1730452358689-1",
                "className": "col row2-col2 hid",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452358689-2",
                "className": "col row2-col3 hid",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            }
        ]
    },
    {
        "id": "row-1730452360022",
        "className": "row row3",
        "childs": [
            {
                "id": "col-1730452348553-16",
                "className": "col row3-col0",
                "content": "<p>课程内容</p><p>（占比）</p>",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452360946",
                "className": "col row3-col1",
                "content": "<p>课程内容<font color=\"#ff5e00\">70%</font></p>",
                "colspan": 3,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452360946-1",
                "className": "col row3-col2 hid",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452360946-2",
                "className": "col row3-col3 hid",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            }
        ]
    },
    {
        "id": "row-1730452362842",
        "className": "row row4",
        "childs": [
            {
                "id": "col-1730452348553-21",
                "className": "col row4-col0",
                "content": "<p>其他适用版本</p>",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452363726",
                "className": "col row4-col1",
                "content": "<p>适用于西师大（现西南版）、青岛六三版、</p><p>冀教版</p>",
                "colspan": 3,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452363726-1",
                "className": "col row4-col2 hid",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            },
            {
                "id": "col-1730452363726-2",
                "className": "col row4-col3 hid",
                "content": "文字",
                "colspan": 1,
                "rowspan": 1,
                "style": ""
            }
        ]
    }
];

export const swipers = [
    {
        id: 'swiper1',
        html: '<div class="img-holder" style="padding-top: 73.6%;"><img src=https://mktc.fbcontent.cn/2024/11/eb2c369c3164.webp loading="lazy"></div>',
        src: 'https://mktc.fbcontent.cn/2024/11/eb2c369c3164.webp',
        href: ''
    },
    {
        id: 'swiper2',
        html: '<div class="img-holder" style="padding-top: 73.6%;"><img src=https://mktc.fbcontent.cn/2024/11/eb2c369c3164.webp loading="lazy"></div>',
        src: 'https://mktc.fbcontent.cn/2024/11/eb2c369c3164.webp',
        href: ''
    },
    {
        id: 'slide3',
        html: '<div class="img-holder" style="padding-top: 73.6%;"><img src=https://mktc.fbcontent.cn/2024/11/7c4d3ba8a0de.webp loading="lazy"></div>',
        src: 'https://mktc.fbcontent.cn/2024/11/7c4d3ba8a0de.webp'
    }
];