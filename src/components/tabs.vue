<template>
    <div  class="box box-custom-tab">
        <div class="tabs">
            <div
                v-for="(tab, index) in tabs"
                :key="tab.id"
                :class="{active: activeIndex === index}"
                @click="activeIndex=index"
                class="tab"
                >
                {{ tab.value }}
            </div>
        </div>
        <div class="panels">
            <div
                v-for="(tab, index) in tabs"
                :key="tab.id"
                :class="{active: activeIndex === index}"
                class="panel"
            >
                <!-- 这里是基地是根据基地的配置，来决定渲染对应的组件，这个项目旨在还原UI，所以下面组件根据自己的需要进行替换 -->
                <Table v-if="activeIndex === index"></Table>
            </div>
        </div>
    </div>
</template>
<script>
import Table from './table.vue';

export default {
    components: {
        Table,
    },
    data() {
        return {
            activeIndex: 0,
            tabs: [
                {
                    id: 0,
                    value: "tag1"
                },
                {
                    id: 1,
                    value: "tag2"
                }
            ],
        };
    }
};


</script>
<style lang="less" scoped>

// 对应基地的自定义样式（右侧最下面）, 注：不是表格本身的样式，表格自定义样式在下面
.custom {
    padding: 15px;
}

.tabs {
  display: -webkit-flex;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: flex-start;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  background: #FFF;
  height: 55px
}

.tabs>input[type='radio'] {
  opacity: 0;
  position: absolute
}

.tabs>.tab {
  width: 115px;
  height: 42px;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 5px;
  background: #FFEABF;
  color: #020202;
  -webkit-box-flex: 0;
  -webkit-flex: none;
  -moz-box-flex: 0;
  -ms-flex: none;
  flex: none;
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  margin-right: 15px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -moz-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding-top: 10px
}

.tabs>.tab:last-of-type {
  margin-right: 0
}

.tabs>.active {
  height: 51px;
  background: url('https://mktb.fbcontent.cn/2024/10/631a9efeba2c.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center
}

.panels {
  padding: 0 18px;
  padding-bottom: 24px;
  background-color: #FFF;
  border-radius: 0 0 20px 20px;
  margin-top: -2px;
  overflow: hidden;
  position: relative;
  z-index: 1
}

.panels>.panel {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  position: relative;
  width: 100%
}

.panel .box {
  overflow: visible !important
}

</style>
