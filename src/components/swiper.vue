<template>
    <div class="box box-swiper" :id="swiperId">
        <Swiper :options="swiperOptions" ref="mySwiper" :key="index" >
            <!-- slides -->
            <SwiperSlide v-for="slide in swipers" :key="slide.id" v-html="slide.html">
            </SwiperSlide>
            <!-- 轮播图控制器 -->
            <div class="swiper-pagination" slot="pagination"></div>
        </Swiper>
    </div>
</template>
<script>

import { swiper, swiperSlide } from 'vue-awesome-swiper';
import 'swiper/dist/css/swiper.css';
import {swipers} from './mock.js';

export default {
    components: {
        Swiper: swiper,
        SwiperSlide: swiperSlide
    },
    data() {
        return {
            swiperId: 'h134335465',
            index: 0,
            swiperOptions: {
                pagination: {
                    el: '.swiper-pagination'
                },
                centeredSlides: true,
                slidesPerView: 'auto',
                loop: true,
                speed: 1000,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false
                },
                effect: 'coverflow'
            },
            swipers: swipers,
        };
    }
};


</script>
<style lang="less">

// 对应基地的自定义样式（右侧最下面）, 注：不是轮播图本身的样式，轮播图自定义样式在下面
.custom {
    padding: 15px;
}

// 下面的样式调试好之后可以直接 粘贴到基地的轮播图样式中即可
.swiper-container {
  padding-top: 19px;
  padding-bottom: 11px;
  background: #fff;
  border-radius: 20px
}

.swiper-container>.swiper-wrapper>.swiper-slide {
  width: 343px;
  margin: 0 1%;
  opacity: .5
}

.swiper-container>.swiper-wrapper>.swiper-slide>.img-holder {
  border-radius: 12px
}

.swiper-container>.swiper-wrapper>.swiper-slide>.img-holder {
  background: none
}

.swiper-container>.swiper-wrapper>.swiper-slide-active {
  opacity: 1
}

.swiper-pagination {
  position: static;
  margin-top: 10px
}

.swiper-pagination>.swiper-pagination-bullet {
  width: 7px;
  height: 7px;
  background: #FFD7AB
}

.swiper-pagination>.swiper-pagination-bullet-active {
  background: #FF8700
}
</style>
