<template>
    <div class="box box-table custom">
        <table>
            <tbody>
                <tr v-for="row in table" :key="row.id" :class="row.className">
                    <td
                        v-for="col in row.childs"
                        :key="col.id"
                        v-html="getContentHtml(col)"
                        :class="col.className"
                        :colspan="col.colspan"
                        :rowspan="col.rowspan"
                        :style="col.style"
                    ></td>
                </tr>
            </tbody>
        </table>
    </div>
</template>
<script>
import {table4Rows2Columns} from './mock.js';

export default {
    data() {
        return {
            // 4行2列 基地配置的表格数据, 使用其他数据请自行在 mock文件修改
            table: table4Rows2Columns,
        };
    },
    methods: {
        getContentHtml(col) {
            let str = '';
            // 使用 $$YFD_USER_DEVICE_INFO$$% 作为设备信息模板字符串
            const content = col.content;
            if(!col.images) {
                return content;
            }
            for (let i = 0; i < col.images.length; i++) {
                str += `<img src=${col.images[i].src}>`;
            }
            return str ? content + `<div>${str}</div>` : content;
        }
    },
};


</script>
<style lang="less" scoped>

// 对应基地的自定义样式（右侧最下面）, 注：不是表格本身的样式，表格自定义样式在下面
.custom {
    padding: 15px;
}

.box-table {
    // 这里的样式是基地自带的样式，请勿改动
    > table {
        width: 100%;
        > tbody > tr {
            > td {
                padding: 8px 0;
                border: 1px solid #eee;
                text-align: center;
                &.active {
                    box-shadow: 0 0 5px red;
                }
                &.hid {
                    display: none;
                }
                > div > img {
                    margin-right: 3px;
                    height: 12px;
                    width: 12px;
                    &:last-child {
                        margin-right: 0px;
                    }
                }
            }
        }
    }

    // 注： 下面为自定义开发样式，可直接粘贴到基地中
    table {
        border-style: hidden;
        border-collapse: separate;
        border-radius: 10px;
        overflow: hidden
    }

    table>tbody {
        background: #fff
    }

    table>tbody>tr>td {
        border: none;
        font-size: 13px;
        color: #7A6566;
        line-height: 17px
    }

    table>tbody>tr>td:first-child {
        width: 80px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 13px;
        color: #020202;
        line-height: 17px;
        letter-spacing: 0;
        text-align: center;
        font-style: normal;
        text-transform: uppercase
    }

    table>tbody>tr:nth-child(odd) td:nth-child(odd) {
        background-color: #FFD28F
    }

    table>tbody>tr:nth-child(odd) td:nth-child(even) {
        background-color: #FFF5E4;
        padding: 16px 27px 16px 7px;
        text-align: left
    }

    table>tbody>tr:nth-child(even) td:nth-child(odd) {
        background-color: #FFF7CD
    }

    table>tbody>tr:nth-child(even) td:nth-child(even) {
        background-color: #FFFBF0;
        padding: 16px 27px 16px 7px;
        text-align: left
    }
}
</style>
