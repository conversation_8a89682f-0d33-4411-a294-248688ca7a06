---
description:
globs:
alwaysApply: false
---
# AI 组件生成指令（Motiff MCP + 项目规则）

## 指令描述
通过本指令，将按项目 rule 及 Motiff MCP 规范，自动生成符合要求的 Vue 组件。

## 使用方法

当我使用此文件指令，入参是 一个 motiff 组件地址<Motiff设计稿链接>，举例：https://beta.miaoduo.com/file/YjlAFibjOOeEbFNDqFsrg2r?nodeId=731%3A8562&type=design

在 Cursor Agent 下输入如下指令

```txt
## 要求
- 严格按照 motiff mcp 响应html结构绘制
- 文件命名：应为小写连字符 book-card.vue。
- 图片：
    - 如果能使用 css 绘制的边框、分界线等基础样式，不要直接使用图片实现。
    - 需要使用图片渲染的元素必须用 <img> 标签加载，不能用 background-image处理。
    - img 标签建议加 loading="lazy"。
- 样式：
    - 禁止使用行内样式。
    - 组件最外层禁止固定高宽。
    - 不要使用 gap、grid 等高级样式。
    - 推荐 BEM 命名。
    - 样式用 less 嵌套。
    - 样式全部写在 <style lang="less" scoped>，不要写在行内。
    - 需考虑移动端自适应，优先用百分比、flex。
    - 应尽量移除不必要的样式。
- 尽量不要使用 script 逻辑参与页面交互及样式设计。
- 兼容性：需要考虑兼容性到 chrome40

## 按照下面步骤逐步完成任务：
1. 认证审查上诉要求，如果 motiff mcp 中要求和我的要求冲突，以我的要求为准，然后帮我基于这个设计稿<Motiff设计稿链接>，在 components 目录下创建一个新的 vue 组件: <Motiff设计稿链接>
2. 直接生成组件文件
3. 检查生成组件样式和 UI 样式差异
4. 生成组件后直接帮我在 src/route.js 中增加新组件路由，import 引入新增组件
```

