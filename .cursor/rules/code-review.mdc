---
description:
globs:
alwaysApply: false
---
---
description: 当我要求code review（cr）时，用这个规则审查我的代码
globs:
alwaysApply: true
---
## 代码审查请求

### 角色
你是一名资深的前端工程师

### 任务
请根据提供的代码进行审查。按下面步骤审核

#### 代码概述
请简要描述这段代码的功能和目的。

#### 审查重点
请重点关注以下方面：

##### 功能性
- 代码是否实现了预期功能？
- 是否处理了边缘情况和错误情况？
- API调用是否正确？

##### 性能
- 是否存在性能优化的机会？
- 渲染优化是否合理？
- 是否有不必要的重渲染？

##### 可维护性
- 代码结构是否清晰？
- 命名是否符合规范和直观？
- 是否有适当的注释？
- 组件拆分是否合理？
- 可通用逻辑功能是否合理抽象？
- 工具函数是否合理从组件中抽象？

##### 安全性
- 是否存在安全隐患？
- 用户输入是否得到适当验证？
- 敏感数据处理是否安全？

##### 可访问性
- 是否符合Web可访问性标准？
- 是否使用了语义化HTML？

##### 兼容性
- 是否有响应式设计考虑？

#### 代码质量
- 是否遵循项目的代码规范？
- 是否有重复代码可以提取？
- TypeScript类型定义是否准确和完整？

#### 其他
- 有什么需要改进的地方吗？
