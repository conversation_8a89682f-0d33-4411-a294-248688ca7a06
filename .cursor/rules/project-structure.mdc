---
description:
globs:
alwaysApply: false
---
# 项目结构与技术栈说明

本项目为“基地项目拖拽生成UI组件使用”，即通过可视化拖拽方式快速生成和预览 UI 组件，适用于活动页、班课详情介绍页场景。

## 主要目录结构
- [src/](mdc:src)：主源码目录
  - [components/](mdc:src/components)：全局/通用 Vue 组件目录，推荐所有可复用组件放在此处
  - [modules/](mdc:src/modules)：业务模块目录，每个子目录为独立业务模块
    - [winterSpringPrimaryLessonIntro-2024/](mdc:src/modules/winterSpringPrimaryLessonIntro-2024)：2024年寒春主推课相关模块
      - [components/](mdc:src/modules/winterSpringPrimaryLessonIntro-2024/components)：该模块下的私有组件
- [less/](mdc:less)：全局 less 样式文件目录，建议全局样式、变量、mixin 放于此
- [public/](mdc:public)：静态资源目录，包含 index.html、favicon 等
- [images/](mdc:images)：图片资源目录，存放本地图片
- [package.json](mdc:package.json)：项目依赖、脚本、钩子配置
- [babel.config.js](mdc:babel.config.js)：Babel 配置
- [postcss.config.js](mdc:postcss.config.js)：PostCSS 配置
- [Makefile](mdc:Makefile)：自动化脚本
- [pnpm-lock.yaml](mdc:pnpm-lock.yaml)：pnpm 依赖锁定文件

## 技术栈说明（参考 package.json）
- **核心框架**：Vue 2.x（^2.6.10），采用 vue-cli 3.x 脚手架
- **路由**：vue-router 3.x
- **UI/交互**：swiper、vue-awesome-swiper
- **样式**：less、less-loader
- **构建工具**：@vue/cli-service、babel、eslint、postcss
- **代码规范**：eslint、@vue/eslint-config-prettier、@vue/eslint-config-standard
- **自动化/钩子**：lint-staged、gitHooks
- **依赖管理**：pnpm

## 组件开发与复用建议
- 通用组件建议放在 [src/components/](mdc:src/components)
- 业务专属组件建议放在对应 [src/modules/xxx/components/](mdc:src/modules) 下
- 组件样式可用 less，推荐使用 scoped 或 BEM 命名
- 静态资源优先放 images/，公共样式放 less/

## @components 组件注意事项

- 组件文件命名规则：使用小写连字符命名，举例：book-card.vue
- 兼容性：需要考虑兼容性到 chrome40
- 尽量不要使用 script 逻辑参与页面交互及样式设计
- 图片：为基地可替换图片，所有图片必须使用 img 标签加载
- 图片：img 标签建议加 loading=\"lazy\"
- 样式：不要使用行内样式
- 样式：禁止将样式定义到组件外，均定义到当前组件 <style scoped> 中
- 样式：应尽量移除不必要的样式
- 样式：需要考虑移动端屏幕自适应，尽量使用百分比、flex 布局方式
- 样式：组件最外层容器上禁止使用固定高宽
- 样式：不要使用 gap、grid 等高级样
- 样式：命名空间建议采用 BEM
- BEM 示例：.book-card__title--highlight { color: red; }
- 样式：<style scoped> 中样式使用less嵌套
- less 嵌套示例：
  .book-card {
    &__title { ... }
    &__desc { ... }
  }

