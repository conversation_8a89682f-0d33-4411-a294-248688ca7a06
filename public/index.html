<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width,initial-scale=1.0"> -->
    <meta name="viewport", content="minimal-ui,width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">

    <!-- meta(name="apple-mobile-web-app-capable", content="yes") -->
    <meta name="apple-touch-fullscreen", content="yes">
    <meta name="format-detection", content="telephone=no,email=no">
    <link rel='icon', href=staticFile('img/favicon.ico')>
    <style>
        html,body{font-size:10px;color:#333;margin:0 auto;}html{background-color:rgba(0,0,0,.5);}body{background-color:#fff;}
    </style>
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>tutor-web-market-page</title>
  </head>
  <body>
        <script>
                (function () {
                var $viewport = document.getElementsByName('viewport')[0],
                    lastOrient = -1,
                    lastClientWidth = 0,
                    htmlWidth = 375,
                    orientScale = [0,0]; //0:portraitScale; 1:landscapeScale;
                function getClientWidth (orient, useBodyWidth) {
                    // 测试了各种手机，
                    // window.outerWidth相当于网页内容显示区域的宽度，而且不受viewport缩放影响
                    // document.documentElement.clientWidth收viewport影响，而且在某些手机（1加手机1）数值异常
                    // screen.availWidth，所有手机上都和screen.width一致，屏幕底部有虚拟按键时，包括了虚拟按键的大小，所以不能使用。
                    var clientWidth = useBodyWidth ? document.body.clientWidth : Math.max(window.outerWidth, document.documentElement.clientWidth);
                    if (clientWidth === lastClientWidth) {
                        setTimeout(function () {
                            getClientWidth(orient);
                        }, 100);
                    } else {
                        var scale = orientScale[orient] = clientWidth / htmlWidth;
                        $viewport.setAttribute('content', 'width='+htmlWidth+', initial-scale='+scale+', maximum-scale='+scale+', minimum-scale='+scale+', target-densitydpi=device-dpi, user-scalable=no');
                        lastClientWidth = clientWidth;
                    }
                }
                function orientationChange (useBodyWidth) {
                    var orient = Math.abs(window.orientation) === 90 ? 1 : 0;
                    if (orient === lastOrient) {
                        return;
                    }
                    lastOrient = orient;
                    if (!orientScale[orient]) {
                        //- $viewport.attr('content','width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, target-densitydpi=device-dpi, user-scalable=no');
                        getClientWidth(orient, useBodyWidth);
                    } else {
                        var scale = orientScale[orient];
                        $viewport.setAttribute('content', 'width='+htmlWidth+', initial-scale='+scale+', maximum-scale='+scale+', minimum-scale='+scale+', target-densitydpi=device-dpi, user-scalable=no');
                    }
                }
                window.addEventListener('orientationchange', function () {
                    orientationChange();
                });
                window.addEventListener('resize', function () {
                    orientationChange();
                });
                orientationChange(true);
            })();</script>
    <noscript>
      <strong>We're sorry but tutor-web-market-page doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
