lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      core-js:
        specifier: ^2.6.5
        version: 2.6.12
      swiper:
        specifier: ^4.0.7
        version: 4.5.1
      vue:
        specifier: ^2.6.10
        version: 2.7.16
      vue-awesome-swiper:
        specifier: '=3.1.3'
        version: 3.1.3
      vue-router:
        specifier: '=3.0.6'
        version: 3.0.6(vue@2.7.16)
    devDependencies:
      '@tutor/ng-cli':
        specifier: ^9.0.47
        version: 9.0.47
      '@vue/cli-plugin-babel':
        specifier: ^3.7.0
        version: 3.12.1(vue@2.7.16)
      '@vue/cli-plugin-eslint':
        specifier: '=3.7.0'
        version: 3.7.0
      '@vue/cli-service':
        specifier: ^3.7.0
        version: 3.12.1(lodash@4.17.21)(prettier@1.19.1)(vue-template-compiler@2.7.16)
      '@vue/eslint-config-prettier':
        specifier: ^4.0.1
        version: 4.0.1(eslint@5.16.0)
      '@vue/eslint-config-standard':
        specifier: ^4.0.0
        version: 4.0.0(eslint@5.16.0)
      babel-eslint:
        specifier: ^10.0.1
        version: 10.1.0(eslint@5.16.0)
      eslint:
        specifier: ^5.16.0
        version: 5.16.0
      eslint-plugin-jsdoc:
        specifier: '=5.0.2'
        version: 5.0.2(eslint@5.16.0)
      eslint-plugin-vue:
        specifier: ^5.0.0
        version: 5.2.3(eslint@5.16.0)
      less:
        specifier: ^3.0.4
        version: 3.13.1
      less-loader:
        specifier: ^4.1.0
        version: 4.1.0(less@3.13.1)(webpack@4.47.0)
      lint-staged:
        specifier: ^8.1.5
        version: 8.2.1
      vue-template-compiler:
        specifier: ^2.5.21
        version: 2.7.16

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=, tarball: http://npm.zhenguanyu.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha1-S1+rl9MzOO/5FiNQVfDrwh5XOoU=, tarball: http://npm.zhenguanyu.com/@babel/code-frame/download/@babel/code-frame-7.26.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.2':
    resolution: {integrity: sha1-J4trE2ZFV96VuPNbkNlnhYULtW4=, tarball: http://npm.zhenguanyu.com/@babel/compat-data/download/@babel/compat-data-7.26.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.0':
    resolution: {integrity: sha1-14tgI8yPMRTM8EnrIZYT90p0e0A=, tarball: http://npm.zhenguanyu.com/@babel/core/download/@babel/core-7.26.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.26.2':
    resolution: {integrity: sha1-h7dYE77IeRYhDl4Bk5pMgj1rt08=, tarball: http://npm.zhenguanyu.com/@babel/generator/download/@babel/generator-7.26.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.25.9':
    resolution: {integrity: sha1-2OrE0twNe24R+m5TUzLg0xhPBrQ=, tarball: http://npm.zhenguanyu.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-builder-binary-assignment-operator-visitor@7.25.9':
    resolution: {integrity: sha1-9BdS/ncqV45nKG5neaaKWpLeHuk=, tarball: http://npm.zhenguanyu.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.25.9':
    resolution: {integrity: sha1-Va8CXONlvjzcDBweVsavYXzoiHU=, tarball: http://npm.zhenguanyu.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.25.9':
    resolution: {integrity: sha1-dkQUdwa7kP9hMpfUntUma95yn4M=, tarball: http://npm.zhenguanyu.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.25.9':
    resolution: {integrity: sha1-PomZ25RyitKyRY16Rw53cLd2TiY=, tarball: http://npm.zhenguanyu.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.2':
    resolution: {integrity: sha1-GFlPeJw1lKyyTP20p/e30ui9kS0=, tarball: http://npm.zhenguanyu.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.2.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-environment-visitor@7.24.7':
    resolution: {integrity: sha1-SzG6lVHR+QeBuoNJHdWc+bJp99k=, tarball: http://npm.zhenguanyu.com/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.24.7.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.25.9':
    resolution: {integrity: sha1-nf/+RvcnAFpeopBRrINftzXkwaM=, tarball: http://npm.zhenguanyu.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha1-5/jSBgLr2/nrvqCgdR+w8qQUFxU=, tarball: http://npm.zhenguanyu.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha1-jOVOydWSaV5Y2EzYhLe1xqL97q4=, tarball: http://npm.zhenguanyu.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.26.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.25.9':
    resolution: {integrity: sha1-MySuULrn4qs8M/YMmod7agFGtU4=, tarball: http://npm.zhenguanyu.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.25.9':
    resolution: {integrity: sha1-nL3WOpRDoskqclzKfryhLMjdn0Y=, tarball: http://npm.zhenguanyu.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.25.9':
    resolution: {integrity: sha1-5TlWqz1bn7iL4Es+LzG1I6/TS5I=, tarball: http://npm.zhenguanyu.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.25.9':
    resolution: {integrity: sha1-ukRyJHmMPaP4cT/CcrFF4z2mpcU=, tarball: http://npm.zhenguanyu.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-simple-access@7.25.9':
    resolution: {integrity: sha1-bVF4MpmISix0YY1u8PhoIOwudzk=, tarball: http://npm.zhenguanyu.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    resolution: {integrity: sha1-Cy4bYtVg1rGVSJP9K3BdwXyR8Mk=, tarball: http://npm.zhenguanyu.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=, tarball: http://npm.zhenguanyu.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=, tarball: http://npm.zhenguanyu.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha1-huRb2KSat+A/J2V3+WF5ZT1B2nI=, tarball: http://npm.zhenguanyu.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.25.9':
    resolution: {integrity: sha1-2Z39WVMS5siUvX0jdHACXIXuqdA=, tarball: http://npm.zhenguanyu.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.26.0':
    resolution: {integrity: sha1-MOYh8eulqkX+b0ho0ukVTYhBGaQ=, tarball: http://npm.zhenguanyu.com/@babel/helpers/download/@babel/helpers-7.26.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.26.2':
    resolution: {integrity: sha1-/XtvSHz+oJiJVX711O65/5pavRE=, tarball: http://npm.zhenguanyu.com/@babel/parser/download/@babel/parser-7.26.2.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-proposal-async-generator-functions@7.20.7':
    resolution: {integrity: sha1-v7cnbS1XPLZ7o3mYSiM04mK6UyY=, tarball: http://npm.zhenguanyu.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.20.7.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-async-generator-functions instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-class-properties@7.18.6':
    resolution: {integrity: sha1-sRD1l0GJX37CGm//aW7EYmXERqM=, tarball: http://npm.zhenguanyu.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-decorators@7.25.9':
    resolution: {integrity: sha1-hoBwf5Q9Gj2izWa5SBeZIPCX4lQ=, tarball: http://npm.zhenguanyu.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-json-strings@7.18.6':
    resolution: {integrity: sha1-foeIwYEcOTr/digX59vx69DAXws=, tarball: http://npm.zhenguanyu.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-json-strings instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-object-rest-spread@7.20.7':
    resolution: {integrity: sha1-qmYpQO9CV3nHVTSlxB6dk27cOQo=, tarball: http://npm.zhenguanyu.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.20.7.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-object-rest-spread instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-optional-catch-binding@7.18.6':
    resolution: {integrity: sha1-+UANDmo+qTup73CwnnLdbaY4oss=, tarball: http://npm.zhenguanyu.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-optional-catch-binding instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-unicode-property-regex@7.18.6':
    resolution: {integrity: sha1-r2E9LNXmQ2Q7Zc3tZCB7Fchct44=, tarball: http://npm.zhenguanyu.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.18.6.tgz}
    engines: {node: '>=4'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-unicode-property-regex instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=, tarball: http://npm.zhenguanyu.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-decorators@7.25.9':
    resolution: {integrity: sha1-mGtMqLe13z9nzuiJzt7/wuK/FLM=, tarball: http://npm.zhenguanyu.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-dynamic-import@7.8.3':
    resolution: {integrity: sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=, tarball: http://npm.zhenguanyu.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=, tarball: http://npm.zhenguanyu.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.25.9':
    resolution: {integrity: sha1-o0MToXjqVvGVFZm5KcHOrO5xkpA=, tarball: http://npm.zhenguanyu.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=, tarball: http://npm.zhenguanyu.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=, tarball: http://npm.zhenguanyu.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-arrow-functions@7.25.9':
    resolution: {integrity: sha1-eCHUQQvuXaqtu0zdmmZJcE4XaEU=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.25.9':
    resolution: {integrity: sha1-yAAI2srlFIJ5PlqcCLOaW+fhLXE=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.25.9':
    resolution: {integrity: sha1-VwBpHb16u5PeMAynvpQgN2T85Fg=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.25.9':
    resolution: {integrity: sha1-wzZl5GsGdZyTaHyg+EOVuAwEc6E=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-classes@7.25.9':
    resolution: {integrity: sha1-cVJFf3iAtZOmOt6Khh5uJqRGn1I=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.25.9':
    resolution: {integrity: sha1-2zZJLHhGDlNLiFKx1b7+PJI+8Qs=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.25.9':
    resolution: {integrity: sha1-lm6iWVxJgiQ0CINgLTz9egx5zqE=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.25.9':
    resolution: {integrity: sha1-uteUXdB3NMpS/jrU6HK0DtCbsJo=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.25.9':
    resolution: {integrity: sha1-iFDd9X3OKuu0OUu0NKdZgDEFnm0=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.25.9':
    resolution: {integrity: sha1-7OR7cNI2wdmcJjoeIrYtwgpMiw8=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.25.9':
    resolution: {integrity: sha1-S9x9QqITOXkF2J8CNQxSZ4ZtV1U=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.25.9':
    resolution: {integrity: sha1-k52VbmimBmYQBb/VUMT8Lvlfe5c=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.25.9':
    resolution: {integrity: sha1-GhxrTUqlm8TK1bazoiOgq9aFyd4=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.25.9':
    resolution: {integrity: sha1-SbpHjyKVEBVEq9eUSGzTCI3dtsU=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.25.9':
    resolution: {integrity: sha1-0WXIxWmggLr1RnvaiN9kJfwGBoY=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.25.9':
    resolution: {integrity: sha1-i9G0ODYmnj0zMHFRoRS887pnk/g=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.25.9':
    resolution: {integrity: sha1-ZxAHnN18aU2zZSmh6EEeSfy/FMk=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha1-RUmQrmzCL9Kg+mCzosb2OjgGTmo=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.25.9':
    resolution: {integrity: sha1-QuYXESlLEFwkgzbcsEt3BU6ovs0=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.25.9':
    resolution: {integrity: sha1-OF1d4TUWKTO+tKPSJ6K35Su0zwM=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.25.9':
    resolution: {integrity: sha1-uFaEIgWz534Yt6ehuUlYBpx7olc=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.25.9':
    resolution: {integrity: sha1-A6ikZw1s666VMFrG3vrIHs53dAs=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-runtime@7.25.9':
    resolution: {integrity: sha1-YnI+o/WzH/vmdtqdba4XE4rlgOo=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.25.9':
    resolution: {integrity: sha1-u3heYJH5n4JqlfmJT8Fv3mHBY/I=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.25.9':
    resolution: {integrity: sha1-JKNRU5MbS6PRPOxKd0jCGrVRTvk=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.25.9':
    resolution: {integrity: sha1-x/ArlE6YakF4F7ILosUE38FFPTI=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.25.9':
    resolution: {integrity: sha1-bb1KJOj60CTfdtH6xqA89BP2D+E=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.25.9':
    resolution: {integrity: sha1-IkukipKGndv4H5tKXxIEu/WivEs=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.25.9':
    resolution: {integrity: sha1-Xq50f+OerPE6i9AGpPsLXR+l6bE=, tarball: http://npm.zhenguanyu.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-env@7.3.4':
    resolution: {integrity: sha1-iHzzi20jyC8ZtRNSmL2xYAYuM+E=, tarball: http://npm.zhenguanyu.com/@babel/preset-env/download/@babel/preset-env-7.3.4.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime-corejs2@7.26.0':
    resolution: {integrity: sha1-pJ1omkMuJ7m/6k80+ABqvKjLr7U=, tarball: http://npm.zhenguanyu.com/@babel/runtime-corejs2/download/@babel/runtime-corejs2-7.26.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/runtime@7.26.0':
    resolution: {integrity: sha1-hgDC9ZXyd8YIFSVkGLhTVqZRc8E=, tarball: http://npm.zhenguanyu.com/@babel/runtime/download/@babel/runtime-7.26.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.25.9':
    resolution: {integrity: sha1-7LYtgaim9dxf6Kv8OQH8Ut3xUBY=, tarball: http://npm.zhenguanyu.com/@babel/template/download/@babel/template-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.25.9':
    resolution: {integrity: sha1-pQ+P5J5/afU95b6n5BPNNcXhPIQ=, tarball: http://npm.zhenguanyu.com/@babel/traverse/download/@babel/traverse-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.26.0':
    resolution: {integrity: sha1-3qvQjWt1O8jg8Zj4cJ+1deMXdP8=, tarball: http://npm.zhenguanyu.com/@babel/types/download/@babel/types-7.26.0.tgz}
    engines: {node: '>=6.9.0'}

  '@hapi/address@2.1.4':
    resolution: {integrity: sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=, tarball: http://npm.zhenguanyu.com/@hapi/address/download/@hapi/address-2.1.4.tgz}
    deprecated: Moved to 'npm install @sideway/address'

  '@hapi/bourne@1.3.2':
    resolution: {integrity: sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=, tarball: http://npm.zhenguanyu.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz}
    deprecated: This version has been deprecated and is no longer supported or maintained

  '@hapi/hoek@8.5.1':
    resolution: {integrity: sha1-/elgZMpEbeyMVajC8TCVewcMbgY=, tarball: http://npm.zhenguanyu.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz}
    deprecated: This version has been deprecated and is no longer supported or maintained

  '@hapi/joi@15.1.1':
    resolution: {integrity: sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=, tarball: http://npm.zhenguanyu.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz}
    deprecated: Switch to 'npm install joi'

  '@hapi/topo@3.1.6':
    resolution: {integrity: sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=, tarball: http://npm.zhenguanyu.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz}
    deprecated: This version has been deprecated and is no longer supported or maintained

  '@intervolga/optimize-cssnano-plugin@1.0.6':
    resolution: {integrity: sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg=, tarball: http://npm.zhenguanyu.com/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz}
    peerDependencies:
      webpack: ^4.0.0

  '@jridgewell/gen-mapping@0.3.5':
    resolution: {integrity: sha1-3M5q/3S99trRqVgCtpsEovyx+zY=, tarball: http://npm.zhenguanyu.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.5.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=, tarball: http://npm.zhenguanyu.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=, tarball: http://npm.zhenguanyu.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=, tarball: http://npm.zhenguanyu.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=, tarball: http://npm.zhenguanyu.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz}

  '@mrmlnc/readdir-enhanced@2.2.1':
    resolution: {integrity: sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=, tarball: http://npm.zhenguanyu.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz}
    engines: {node: '>=4'}

  '@nodelib/fs.stat@1.1.3':
    resolution: {integrity: sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=, tarball: http://npm.zhenguanyu.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz}
    engines: {node: '>= 6'}

  '@rtsao/scc@1.1.0':
    resolution: {integrity: sha1-kn3S+um8M2FAOsLHoAwy3c6a1+g=, tarball: http://npm.zhenguanyu.com/@rtsao/scc/download/@rtsao/scc-1.1.0.tgz}

  '@samverschueren/stream-to-observable@0.3.1':
    resolution: {integrity: sha1-ohEXsZ7pvnDDeewYd1N+8uHGMwE=, tarball: http://npm.zhenguanyu.com/@samverschueren/stream-to-observable/download/@samverschueren/stream-to-observable-0.3.1.tgz}
    engines: {node: '>=6'}
    peerDependencies:
      rxjs: '*'
      zen-observable: '*'
    peerDependenciesMeta:
      rxjs:
        optional: true
      zen-observable:
        optional: true

  '@soda/friendly-errors-webpack-plugin@1.8.1':
    resolution: {integrity: sha1-TU+7EQiZOqo2IRYkfD0YGIosbIU=, tarball: http://npm.zhenguanyu.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.8.1.tgz}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  '@tutor/ng-cli@9.0.47':
    resolution: {integrity: sha1-GYtJWtJehuc55r5nrg0OVC8ZmMI=, tarball: http://npm.zhenguanyu.com/@tutor/ng-cli/download/@tutor/ng-cli-9.0.47.tgz}
    engines: {node: '>=13.8.0'}
    hasBin: true

  '@types/glob@7.2.0':
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=, tarball: http://npm.zhenguanyu.com/@types/glob/download/@types/glob-7.2.0.tgz}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=, tarball: http://npm.zhenguanyu.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz}

  '@types/json5@0.0.29':
    resolution: {integrity: sha1-7ihweulOEdK4J7y+UnC86n8+ce4=, tarball: http://npm.zhenguanyu.com/@types/json5/download/@types/json5-0.0.29.tgz}

  '@types/minimatch@5.1.2':
    resolution: {integrity: sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=, tarball: http://npm.zhenguanyu.com/@types/minimatch/download/@types/minimatch-5.1.2.tgz}

  '@types/node@22.8.6':
    resolution: {integrity: sha1-6KDAhxYjKD2LPvfXubG/39MCjiI=, tarball: http://npm.zhenguanyu.com/@types/node/download/@types/node-22.8.6.tgz}

  '@types/normalize-package-data@2.4.4':
    resolution: {integrity: sha1-VuLMJsOXwDj6sOOpF6EtXFkJ6QE=, tarball: http://npm.zhenguanyu.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.4.tgz}

  '@types/q@1.5.8':
    resolution: {integrity: sha1-lfbGoI8q2Gi6Iw6tHS1/e+PbODc=, tarball: http://npm.zhenguanyu.com/@types/q/download/@types/q-1.5.8.tgz}

  '@vue/babel-helper-vue-jsx-merge-props@1.4.0':
    resolution: {integrity: sha1-jVOh4hNH247b5U0zmQJYMXbeCfI=, tarball: http://npm.zhenguanyu.com/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.4.0.tgz}

  '@vue/babel-plugin-transform-vue-jsx@1.4.0':
    resolution: {integrity: sha1-TUs9RqOepit0Z91uJs5H986vsv4=, tarball: http://npm.zhenguanyu.com/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/babel-preset-app@3.12.1':
    resolution: {integrity: sha1-JMR3BS8HjzD9t3NRA7FN0fosv+E=, tarball: http://npm.zhenguanyu.com/@vue/babel-preset-app/download/@vue/babel-preset-app-3.12.1.tgz}

  '@vue/babel-preset-jsx@1.4.0':
    resolution: {integrity: sha1-9JFLoxQjWrCXvENy7WdHPAeAv8w=, tarball: http://npm.zhenguanyu.com/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
      vue: '*'
    peerDependenciesMeta:
      vue:
        optional: true

  '@vue/babel-sugar-composition-api-inject-h@1.4.0':
    resolution: {integrity: sha1-GH4TifiHHYns50O7UK7XE76dbIU=, tarball: http://npm.zhenguanyu.com/@vue/babel-sugar-composition-api-inject-h/download/@vue/babel-sugar-composition-api-inject-h-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/babel-sugar-composition-api-render-instance@1.4.0':
    resolution: {integrity: sha1-LBYHrm3/2rR+eFvAH6Rbp1bpksE=, tarball: http://npm.zhenguanyu.com/@vue/babel-sugar-composition-api-render-instance/download/@vue/babel-sugar-composition-api-render-instance-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/babel-sugar-functional-vue@1.4.0':
    resolution: {integrity: sha1-YNoxBoVnCCKHxzN8Zu9N8E4KECk=, tarball: http://npm.zhenguanyu.com/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/babel-sugar-inject-h@1.4.0':
    resolution: {integrity: sha1-vzmqZjH7HQOZscSbTFnhyImbQ2M=, tarball: http://npm.zhenguanyu.com/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/babel-sugar-v-model@1.4.0':
    resolution: {integrity: sha1-pR2YZgn0MMT3Cto6k8xWCilw9yA=, tarball: http://npm.zhenguanyu.com/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/babel-sugar-v-on@1.4.0':
    resolution: {integrity: sha1-Q7cQapZy2Mvu/A64r+HTdu3GFm4=, tarball: http://npm.zhenguanyu.com/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/cli-overlay@3.12.1':
    resolution: {integrity: sha1-vf3o9xI1YasG5OTGC4VMxQkvWrE=, tarball: http://npm.zhenguanyu.com/@vue/cli-overlay/download/@vue/cli-overlay-3.12.1.tgz}

  '@vue/cli-plugin-babel@3.12.1':
    resolution: {integrity: sha1-mnkVnejNCGsBP6bXijmDCy4uxwY=, tarball: http://npm.zhenguanyu.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-3.12.1.tgz}

  '@vue/cli-plugin-eslint@3.7.0':
    resolution: {integrity: sha1-a0lf48guyUNHxCSp3jzKRnpT+Q4=, tarball: http://npm.zhenguanyu.com/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-3.7.0.tgz}

  '@vue/cli-service@3.12.1':
    resolution: {integrity: sha1-EyILHBiSVOfAAzkN8ykIb5tud+Y=, tarball: http://npm.zhenguanyu.com/@vue/cli-service/download/@vue/cli-service-3.12.1.tgz}
    engines: {node: '>=8'}
    hasBin: true
    peerDependencies:
      vue-template-compiler: ^2.0.0

  '@vue/cli-shared-utils@3.12.1':
    resolution: {integrity: sha1-vPB2KH3a3uu7l8anSN/p/1DsjfA=, tarball: http://npm.zhenguanyu.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-3.12.1.tgz}

  '@vue/compiler-sfc@2.7.16':
    resolution: {integrity: sha1-/4FxGg+snGhoPYuwC2P4V9533IM=, tarball: http://npm.zhenguanyu.com/@vue/compiler-sfc/download/@vue/compiler-sfc-2.7.16.tgz}

  '@vue/component-compiler-utils@3.3.0':
    resolution: {integrity: sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck=, tarball: http://npm.zhenguanyu.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.3.0.tgz}

  '@vue/eslint-config-prettier@4.0.1':
    resolution: {integrity: sha1-oDbQ0hk8XINlQrNaOnw1xOHGjJc=, tarball: http://npm.zhenguanyu.com/@vue/eslint-config-prettier/download/@vue/eslint-config-prettier-4.0.1.tgz}

  '@vue/eslint-config-standard@4.0.0':
    resolution: {integrity: sha1-a+RH7mdOOw9zPFhAmP2aIubXb80=, tarball: http://npm.zhenguanyu.com/@vue/eslint-config-standard/download/@vue/eslint-config-standard-4.0.0.tgz}

  '@vue/preload-webpack-plugin@1.1.2':
    resolution: {integrity: sha1-zrkktOyzucQ4ccekKaAvhCPmIas=, tarball: http://npm.zhenguanyu.com/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.2.tgz}
    engines: {node: '>=6.0.0'}
    peerDependencies:
      html-webpack-plugin: '>=2.26.0'
      webpack: '>=4.0.0'

  '@vue/web-component-wrapper@1.3.0':
    resolution: {integrity: sha1-trQKdiVCnSvXwigd26YB7QXcfxo=, tarball: http://npm.zhenguanyu.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.3.0.tgz}

  '@webassemblyjs/ast@1.7.11':
    resolution: {integrity: sha1-uYhYLK+7Kwlei1VlJvMMkNBXys4=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.7.11.tgz}

  '@webassemblyjs/ast@1.9.0':
    resolution: {integrity: sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz}

  '@webassemblyjs/floating-point-hex-parser@1.7.11':
    resolution: {integrity: sha1-pp8K9lAuuaPARVVbGmEp09Py4xM=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.7.11.tgz}

  '@webassemblyjs/floating-point-hex-parser@1.9.0':
    resolution: {integrity: sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz}

  '@webassemblyjs/helper-api-error@1.7.11':
    resolution: {integrity: sha1-x7a7gQX4QDlRGis5zklPGTgYoyo=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.7.11.tgz}

  '@webassemblyjs/helper-api-error@1.9.0':
    resolution: {integrity: sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz}

  '@webassemblyjs/helper-buffer@1.7.11':
    resolution: {integrity: sha1-MSLUjcxslFbtmC3r4WyPNxAd85s=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.7.11.tgz}

  '@webassemblyjs/helper-buffer@1.9.0':
    resolution: {integrity: sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz}

  '@webassemblyjs/helper-code-frame@1.7.11':
    resolution: {integrity: sha1-z48QbnRmYqDaKb3vY1/NPRJINks=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.7.11.tgz}

  '@webassemblyjs/helper-code-frame@1.9.0':
    resolution: {integrity: sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz}

  '@webassemblyjs/helper-fsm@1.7.11':
    resolution: {integrity: sha1-3ziIKmJAgNA/dQP5Pj8XrFrAEYE=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.7.11.tgz}

  '@webassemblyjs/helper-fsm@1.9.0':
    resolution: {integrity: sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz}

  '@webassemblyjs/helper-module-context@1.7.11':
    resolution: {integrity: sha1-2HTXIuUeYqwgJHaTXWScgC+g4gk=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.7.11.tgz}

  '@webassemblyjs/helper-module-context@1.9.0':
    resolution: {integrity: sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz}

  '@webassemblyjs/helper-wasm-bytecode@1.7.11':
    resolution: {integrity: sha1-3ZoegX8cLrEFtM8QEwk8ufPJywY=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.7.11.tgz}

  '@webassemblyjs/helper-wasm-bytecode@1.9.0':
    resolution: {integrity: sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz}

  '@webassemblyjs/helper-wasm-section@1.7.11':
    resolution: {integrity: sha1-nJrEHs+fvP/8lvbSZ14t4zgR5oo=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.7.11.tgz}

  '@webassemblyjs/helper-wasm-section@1.9.0':
    resolution: {integrity: sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz}

  '@webassemblyjs/ieee754@1.7.11':
    resolution: {integrity: sha1-yVg562N1ejGICq7HtlEtQZGsZAs=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.7.11.tgz}

  '@webassemblyjs/ieee754@1.9.0':
    resolution: {integrity: sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz}

  '@webassemblyjs/leb128@1.7.11':
    resolution: {integrity: sha1-1yZ6HunEWU/T9+NymIGOxlaH22M=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.7.11.tgz}

  '@webassemblyjs/leb128@1.9.0':
    resolution: {integrity: sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz}

  '@webassemblyjs/utf8@1.7.11':
    resolution: {integrity: sha1-Btchjqn9yUpnk6qSIIFg2z0m7oI=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.7.11.tgz}

  '@webassemblyjs/utf8@1.9.0':
    resolution: {integrity: sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz}

  '@webassemblyjs/wasm-edit@1.7.11':
    resolution: {integrity: sha1-jHTKR01PlR0B266b1wgU7iKoIAU=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.7.11.tgz}

  '@webassemblyjs/wasm-edit@1.9.0':
    resolution: {integrity: sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz}

  '@webassemblyjs/wasm-gen@1.7.11':
    resolution: {integrity: sha1-m7upQvIjdWhqb7dZr816ycRdoag=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.7.11.tgz}

  '@webassemblyjs/wasm-gen@1.9.0':
    resolution: {integrity: sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz}

  '@webassemblyjs/wasm-opt@1.7.11':
    resolution: {integrity: sha1-szHo5874+OLwB9QsOjagWAp9bKc=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.7.11.tgz}

  '@webassemblyjs/wasm-opt@1.9.0':
    resolution: {integrity: sha1-IhEYHlsxMmRDzIES658LkChyGmE=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz}

  '@webassemblyjs/wasm-parser@1.7.11':
    resolution: {integrity: sha1-bj0g+mo1GfawhO+Tka1YIR77Cho=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.7.11.tgz}

  '@webassemblyjs/wasm-parser@1.9.0':
    resolution: {integrity: sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz}

  '@webassemblyjs/wast-parser@1.7.11':
    resolution: {integrity: sha1-Jb0RdWLKjAAnIP+BFu+QctnKhpw=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.7.11.tgz}

  '@webassemblyjs/wast-parser@1.9.0':
    resolution: {integrity: sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz}

  '@webassemblyjs/wast-printer@1.7.11':
    resolution: {integrity: sha1-xCRbbeJCy1CizJUBdP2/ZceNeBM=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.7.11.tgz}

  '@webassemblyjs/wast-printer@1.9.0':
    resolution: {integrity: sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=, tarball: http://npm.zhenguanyu.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz}

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=, tarball: http://npm.zhenguanyu.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz}

  '@xtuc/long@4.2.1':
    resolution: {integrity: sha1-XIXWYvdvodNFdXZsXc1mFavNMNg=, tarball: http://npm.zhenguanyu.com/@xtuc/long/download/@xtuc/long-4.2.1.tgz}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=, tarball: http://npm.zhenguanyu.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz}

  accepts@1.3.8:
    resolution: {integrity: sha1-C/C+EltnAUrcsLCSHmLbe//hay4=, tarball: http://npm.zhenguanyu.com/accepts/download/accepts-1.3.8.tgz}
    engines: {node: '>= 0.6'}

  acorn-dynamic-import@3.0.0:
    resolution: {integrity: sha1-kBzu5Mf6rvfgetKkfokGddpQong=, tarball: http://npm.zhenguanyu.com/acorn-dynamic-import/download/acorn-dynamic-import-3.0.0.tgz}
    deprecated: This is probably built in to whatever tool you're using. If you still need it... idk

  acorn-jsx@3.0.1:
    resolution: {integrity: sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=, tarball: http://npm.zhenguanyu.com/acorn-jsx/download/acorn-jsx-3.0.1.tgz}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=, tarball: http://npm.zhenguanyu.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn-walk@6.2.0:
    resolution: {integrity: sha1-Ejy487hMIXHx9/slJhWxx4prGow=, tarball: http://npm.zhenguanyu.com/acorn-walk/download/acorn-walk-6.2.0.tgz}
    engines: {node: '>=0.4.0'}

  acorn-walk@7.2.0:
    resolution: {integrity: sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=, tarball: http://npm.zhenguanyu.com/acorn-walk/download/acorn-walk-7.2.0.tgz}
    engines: {node: '>=0.4.0'}

  acorn@3.3.0:
    resolution: {integrity: sha1-ReN/s56No/JbruP/U2niu18iAXo=, tarball: http://npm.zhenguanyu.com/acorn/download/acorn-3.3.0.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@5.7.4:
    resolution: {integrity: sha1-Po2KmUfQWZoXltECJddDL0pKz14=, tarball: http://npm.zhenguanyu.com/acorn/download/acorn-5.7.4.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@6.4.2:
    resolution: {integrity: sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=, tarball: http://npm.zhenguanyu.com/acorn/download/acorn-6.4.2.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@7.4.1:
    resolution: {integrity: sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=, tarball: http://npm.zhenguanyu.com/acorn/download/acorn-7.4.1.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.14.0:
    resolution: {integrity: sha1-Bj4scMrF+09kZ/CxEVLgTGgnlbA=, tarball: http://npm.zhenguanyu.com/acorn/download/acorn-8.14.0.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  address@1.2.2:
    resolution: {integrity: sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=, tarball: http://npm.zhenguanyu.com/address/download/address-1.2.2.tgz}
    engines: {node: '>= 10.0.0'}

  ajv-errors@1.0.1:
    resolution: {integrity: sha1-81mGrOuRr63sQQL72FAUlQzvpk0=, tarball: http://npm.zhenguanyu.com/ajv-errors/download/ajv-errors-1.0.1.tgz}
    peerDependencies:
      ajv: '>=5.0.0'

  ajv-keywords@2.1.1:
    resolution: {integrity: sha1-YXmX/F9gV2iUxDX5QNgZ4TW4B2I=, tarball: http://npm.zhenguanyu.com/ajv-keywords/download/ajv-keywords-2.1.1.tgz}
    peerDependencies:
      ajv: ^5.0.0

  ajv-keywords@3.5.2:
    resolution: {integrity: sha1-MfKdpatuANHC0yms97WSlhTVAU0=, tarball: http://npm.zhenguanyu.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz}
    peerDependencies:
      ajv: ^6.9.1

  ajv@5.5.2:
    resolution: {integrity: sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=, tarball: http://npm.zhenguanyu.com/ajv/download/ajv-5.5.2.tgz}

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=, tarball: http://npm.zhenguanyu.com/ajv/download/ajv-6.12.6.tgz}

  alphanum-sort@1.0.2:
    resolution: {integrity: sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=, tarball: http://npm.zhenguanyu.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz}

  ansi-colors@3.2.4:
    resolution: {integrity: sha1-46PaS/uubIapwoViXeEkojQCb78=, tarball: http://npm.zhenguanyu.com/ansi-colors/download/ansi-colors-3.2.4.tgz}
    engines: {node: '>=6'}

  ansi-escapes@3.2.0:
    resolution: {integrity: sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=, tarball: http://npm.zhenguanyu.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz}
    engines: {node: '>=4'}

  ansi-html-community@0.0.8:
    resolution: {integrity: sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=, tarball: http://npm.zhenguanyu.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  ansi-regex@2.1.1:
    resolution: {integrity: sha1-w7M6te42DYbg5ijwRorn7yfWVN8=, tarball: http://npm.zhenguanyu.com/ansi-regex/download/ansi-regex-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  ansi-regex@3.0.1:
    resolution: {integrity: sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=, tarball: http://npm.zhenguanyu.com/ansi-regex/download/ansi-regex-3.0.1.tgz}
    engines: {node: '>=4'}

  ansi-regex@4.1.1:
    resolution: {integrity: sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=, tarball: http://npm.zhenguanyu.com/ansi-regex/download/ansi-regex-4.1.1.tgz}
    engines: {node: '>=6'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=, tarball: http://npm.zhenguanyu.com/ansi-regex/download/ansi-regex-5.0.1.tgz}
    engines: {node: '>=8'}

  ansi-styles@2.2.1:
    resolution: {integrity: sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=, tarball: http://npm.zhenguanyu.com/ansi-styles/download/ansi-styles-2.2.1.tgz}
    engines: {node: '>=0.10.0'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=, tarball: http://npm.zhenguanyu.com/ansi-styles/download/ansi-styles-3.2.1.tgz}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=, tarball: http://npm.zhenguanyu.com/ansi-styles/download/ansi-styles-4.3.0.tgz}
    engines: {node: '>=8'}

  any-observable@0.3.0:
    resolution: {integrity: sha1-r5M0deWAamfQ198JDdXovvZdEZs=, tarball: http://npm.zhenguanyu.com/any-observable/download/any-observable-0.3.0.tgz}
    engines: {node: '>=6'}
    peerDependencies:
      rxjs: '*'
      zenObservable: '*'
    peerDependenciesMeta:
      rxjs:
        optional: true
      zenObservable:
        optional: true

  any-promise@1.3.0:
    resolution: {integrity: sha1-q8av7tzqUugJzcA3au0845Y10X8=, tarball: http://npm.zhenguanyu.com/any-promise/download/any-promise-1.3.0.tgz}

  anymatch@2.0.0:
    resolution: {integrity: sha1-vLJLTzeTTZqnrBe0ra+J58du8us=, tarball: http://npm.zhenguanyu.com/anymatch/download/anymatch-2.0.0.tgz}

  anymatch@3.1.3:
    resolution: {integrity: sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=, tarball: http://npm.zhenguanyu.com/anymatch/download/anymatch-3.1.3.tgz}
    engines: {node: '>= 8'}

  aproba@1.2.0:
    resolution: {integrity: sha1-aALmJk79GMeQobDVF/DyYnvyyUo=, tarball: http://npm.zhenguanyu.com/aproba/download/aproba-1.2.0.tgz}

  arch@2.2.0:
    resolution: {integrity: sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE=, tarball: http://npm.zhenguanyu.com/arch/download/arch-2.2.0.tgz}

  argparse@1.0.10:
    resolution: {integrity: sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=, tarball: http://npm.zhenguanyu.com/argparse/download/argparse-1.0.10.tgz}

  arr-diff@4.0.0:
    resolution: {integrity: sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=, tarball: http://npm.zhenguanyu.com/arr-diff/download/arr-diff-4.0.0.tgz}
    engines: {node: '>=0.10.0'}

  arr-flatten@1.1.0:
    resolution: {integrity: sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=, tarball: http://npm.zhenguanyu.com/arr-flatten/download/arr-flatten-1.1.0.tgz}
    engines: {node: '>=0.10.0'}

  arr-union@3.1.0:
    resolution: {integrity: sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=, tarball: http://npm.zhenguanyu.com/arr-union/download/arr-union-3.1.0.tgz}
    engines: {node: '>=0.10.0'}

  array-buffer-byte-length@1.0.1:
    resolution: {integrity: sha1-HlWD7BZ2NUCieuUu7Zn/iZIjVo8=, tarball: http://npm.zhenguanyu.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  array-flatten@1.1.1:
    resolution: {integrity: sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=, tarball: http://npm.zhenguanyu.com/array-flatten/download/array-flatten-1.1.1.tgz}

  array-flatten@2.1.2:
    resolution: {integrity: sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=, tarball: http://npm.zhenguanyu.com/array-flatten/download/array-flatten-2.1.2.tgz}

  array-includes@3.1.8:
    resolution: {integrity: sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=, tarball: http://npm.zhenguanyu.com/array-includes/download/array-includes-3.1.8.tgz}
    engines: {node: '>= 0.4'}

  array-union@1.0.2:
    resolution: {integrity: sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=, tarball: http://npm.zhenguanyu.com/array-union/download/array-union-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  array-uniq@1.0.3:
    resolution: {integrity: sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=, tarball: http://npm.zhenguanyu.com/array-uniq/download/array-uniq-1.0.3.tgz}
    engines: {node: '>=0.10.0'}

  array-unique@0.3.2:
    resolution: {integrity: sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=, tarball: http://npm.zhenguanyu.com/array-unique/download/array-unique-0.3.2.tgz}
    engines: {node: '>=0.10.0'}

  array.prototype.findlastindex@1.2.5:
    resolution: {integrity: sha1-jDWnVccpCHGUU/hxRcoBHjkzTQ0=, tarball: http://npm.zhenguanyu.com/array.prototype.findlastindex/download/array.prototype.findlastindex-1.2.5.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.2:
    resolution: {integrity: sha1-FHYhffjP8X1y7o87oGc421s4fRg=, tarball: http://npm.zhenguanyu.com/array.prototype.flat/download/array.prototype.flat-1.3.2.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.2:
    resolution: {integrity: sha1-yafGgx245xnWzmORkBRsJLvT5Sc=, tarball: http://npm.zhenguanyu.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.2.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.reduce@1.0.7:
    resolution: {integrity: sha1-aq3C+ZWvKcuIfrhm2YHchatvfcc=, tarball: http://npm.zhenguanyu.com/array.prototype.reduce/download/array.prototype.reduce-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.3:
    resolution: {integrity: sha1-CXly9CVeQbw0JeN9w/ZCHPmu/eY=, tarball: http://npm.zhenguanyu.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  arrify@1.0.1:
    resolution: {integrity: sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=, tarball: http://npm.zhenguanyu.com/arrify/download/arrify-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  asn1.js@4.10.1:
    resolution: {integrity: sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=, tarball: http://npm.zhenguanyu.com/asn1.js/download/asn1.js-4.10.1.tgz}

  asn1@0.2.6:
    resolution: {integrity: sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=, tarball: http://npm.zhenguanyu.com/asn1/download/asn1-0.2.6.tgz}

  assert-plus@1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=, tarball: http://npm.zhenguanyu.com/assert-plus/download/assert-plus-1.0.0.tgz}
    engines: {node: '>=0.8'}

  assert@1.5.1:
    resolution: {integrity: sha1-A4qySOT/B457wkhbpuY4hGbHj3Y=, tarball: http://npm.zhenguanyu.com/assert/download/assert-1.5.1.tgz}

  assign-symbols@1.0.0:
    resolution: {integrity: sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=, tarball: http://npm.zhenguanyu.com/assign-symbols/download/assign-symbols-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  astral-regex@1.0.0:
    resolution: {integrity: sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=, tarball: http://npm.zhenguanyu.com/astral-regex/download/astral-regex-1.0.0.tgz}
    engines: {node: '>=4'}

  async-each@1.0.6:
    resolution: {integrity: sha1-UvHZQDgYwXm3Vh4RpdG3frIWDnc=, tarball: http://npm.zhenguanyu.com/async-each/download/async-each-1.0.6.tgz}

  async-limiter@1.0.1:
    resolution: {integrity: sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=, tarball: http://npm.zhenguanyu.com/async-limiter/download/async-limiter-1.0.1.tgz}

  async@2.6.4:
    resolution: {integrity: sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=, tarball: http://npm.zhenguanyu.com/async/download/async-2.6.4.tgz}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=, tarball: http://npm.zhenguanyu.com/asynckit/download/asynckit-0.4.0.tgz}

  atob@2.1.2:
    resolution: {integrity: sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=, tarball: http://npm.zhenguanyu.com/atob/download/atob-2.1.2.tgz}
    engines: {node: '>= 4.5.0'}
    hasBin: true

  autoprefixer@9.8.8:
    resolution: {integrity: sha1-/UvUWVOF+m8GWZ3nSaTV96R0lXo=, tarball: http://npm.zhenguanyu.com/autoprefixer/download/autoprefixer-9.8.8.tgz}
    hasBin: true

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=, tarball: http://npm.zhenguanyu.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  aws-sign2@0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=, tarball: http://npm.zhenguanyu.com/aws-sign2/download/aws-sign2-0.7.0.tgz}

  aws4@1.13.2:
    resolution: {integrity: sha1-CqFnIWllrJR0zPqDiSz7az4eUu8=, tarball: http://npm.zhenguanyu.com/aws4/download/aws4-1.13.2.tgz}

  axios@0.19.2:
    resolution: {integrity: sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=, tarball: http://npm.zhenguanyu.com/axios/download/axios-0.19.2.tgz}
    deprecated: Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410

  babel-code-frame@6.26.0:
    resolution: {integrity: sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=, tarball: http://npm.zhenguanyu.com/babel-code-frame/download/babel-code-frame-6.26.0.tgz}

  babel-eslint@10.1.0:
    resolution: {integrity: sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=, tarball: http://npm.zhenguanyu.com/babel-eslint/download/babel-eslint-10.1.0.tgz}
    engines: {node: '>=6'}
    deprecated: babel-eslint is now @babel/eslint-parser. This package will no longer receive updates.
    peerDependencies:
      eslint: '>= 4.12.1'

  babel-loader@8.4.1:
    resolution: {integrity: sha1-bMt1xm5iw7FE4cXy6uxbj2wIxnU=, tarball: http://npm.zhenguanyu.com/babel-loader/download/babel-loader-8.4.1.tgz}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'

  babel-plugin-dynamic-import-node@2.3.3:
    resolution: {integrity: sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=, tarball: http://npm.zhenguanyu.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz}

  babel-plugin-module-resolver@3.2.0:
    resolution: {integrity: sha1-***************************=, tarball: http://npm.zhenguanyu.com/babel-plugin-module-resolver/download/babel-plugin-module-resolver-3.2.0.tgz}
    engines: {node: '>= 6.0.0'}

  babel-plugin-polyfill-corejs2@0.4.11:
    resolution: {integrity: sha1-MDIN/j/+GjNsFa/c2v1v1hWyXjM=, tarball: http://npm.zhenguanyu.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.11.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.10.6:
    resolution: {integrity: sha1-Le2lfK71D1nFJa60lk07L4Z3EMc=, tarball: http://npm.zhenguanyu.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.10.6.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.2:
    resolution: {integrity: sha1-rdxH4kDt0doQWOvaAwIfOCu6eF4=, tarball: http://npm.zhenguanyu.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.2.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=, tarball: http://npm.zhenguanyu.com/balanced-match/download/balanced-match-1.0.2.tgz}

  base64-js@1.5.1:
    resolution: {integrity: sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=, tarball: http://npm.zhenguanyu.com/base64-js/download/base64-js-1.5.1.tgz}

  base@0.11.2:
    resolution: {integrity: sha1-e95c7RRbbVUakNuH+DxVi060io8=, tarball: http://npm.zhenguanyu.com/base/download/base-0.11.2.tgz}
    engines: {node: '>=0.10.0'}

  batch@0.6.1:
    resolution: {integrity: sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=, tarball: http://npm.zhenguanyu.com/batch/download/batch-0.6.1.tgz}

  bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=, tarball: http://npm.zhenguanyu.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz}

  bfj@6.1.2:
    resolution: {integrity: sha1-MlyGGoIryzWKQceKM7jm4ght3n8=, tarball: http://npm.zhenguanyu.com/bfj/download/bfj-6.1.2.tgz}
    engines: {node: '>= 6.0.0'}

  big.js@3.2.0:
    resolution: {integrity: sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=, tarball: http://npm.zhenguanyu.com/big.js/download/big.js-3.2.0.tgz}

  big.js@5.2.2:
    resolution: {integrity: sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=, tarball: http://npm.zhenguanyu.com/big.js/download/big.js-5.2.2.tgz}

  binary-extensions@1.13.1:
    resolution: {integrity: sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=, tarball: http://npm.zhenguanyu.com/binary-extensions/download/binary-extensions-1.13.1.tgz}
    engines: {node: '>=0.10.0'}

  binary-extensions@2.3.0:
    resolution: {integrity: sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=, tarball: http://npm.zhenguanyu.com/binary-extensions/download/binary-extensions-2.3.0.tgz}
    engines: {node: '>=8'}

  bindings@1.5.0:
    resolution: {integrity: sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=, tarball: http://npm.zhenguanyu.com/bindings/download/bindings-1.5.0.tgz}

  bluebird@3.7.2:
    resolution: {integrity: sha1-nyKcFb4nJFT/qXOs4NvueaGww28=, tarball: http://npm.zhenguanyu.com/bluebird/download/bluebird-3.7.2.tgz}

  bn.js@4.12.0:
    resolution: {integrity: sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=, tarball: http://npm.zhenguanyu.com/bn.js/download/bn.js-4.12.0.tgz}

  bn.js@5.2.1:
    resolution: {integrity: sha1-C8UnpqDRjQqo1bBTjOSnfcz6e3A=, tarball: http://npm.zhenguanyu.com/bn.js/download/bn.js-5.2.1.tgz}

  body-parser@1.20.3:
    resolution: {integrity: sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=, tarball: http://npm.zhenguanyu.com/body-parser/download/body-parser-1.20.3.tgz}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  bonjour@3.5.0:
    resolution: {integrity: sha1-jokKGD2O6aI5OzhExpGkK897yfU=, tarball: http://npm.zhenguanyu.com/bonjour/download/bonjour-3.5.0.tgz}

  boolbase@1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=, tarball: http://npm.zhenguanyu.com/boolbase/download/boolbase-1.0.0.tgz}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=, tarball: http://npm.zhenguanyu.com/brace-expansion/download/brace-expansion-1.1.11.tgz}

  braces@2.3.2:
    resolution: {integrity: sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=, tarball: http://npm.zhenguanyu.com/braces/download/braces-2.3.2.tgz}
    engines: {node: '>=0.10.0'}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=, tarball: http://npm.zhenguanyu.com/braces/download/braces-3.0.3.tgz}
    engines: {node: '>=8'}

  brorand@1.1.0:
    resolution: {integrity: sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=, tarball: http://npm.zhenguanyu.com/brorand/download/brorand-1.1.0.tgz}

  browserify-aes@1.2.0:
    resolution: {integrity: sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=, tarball: http://npm.zhenguanyu.com/browserify-aes/download/browserify-aes-1.2.0.tgz}

  browserify-cipher@1.0.1:
    resolution: {integrity: sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=, tarball: http://npm.zhenguanyu.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz}

  browserify-des@1.0.2:
    resolution: {integrity: sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=, tarball: http://npm.zhenguanyu.com/browserify-des/download/browserify-des-1.0.2.tgz}

  browserify-rsa@4.1.1:
    resolution: {integrity: sha1-BuUwkH/ilJ3CH8PC4jAuELFDcjg=, tarball: http://npm.zhenguanyu.com/browserify-rsa/download/browserify-rsa-4.1.1.tgz}
    engines: {node: '>= 0.10'}

  browserify-sign@4.2.3:
    resolution: {integrity: sha1-ev5MAex+5ZqJpVikt1vYWuYtQgg=, tarball: http://npm.zhenguanyu.com/browserify-sign/download/browserify-sign-4.2.3.tgz}
    engines: {node: '>= 0.12'}

  browserify-zlib@0.2.0:
    resolution: {integrity: sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=, tarball: http://npm.zhenguanyu.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz}

  browserslist@4.24.2:
    resolution: {integrity: sha1-9YRbyRBp29Ve6J+vmCLh2IXRZYA=, tarball: http://npm.zhenguanyu.com/browserslist/download/browserslist-4.24.2.tgz}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=, tarball: http://npm.zhenguanyu.com/buffer-from/download/buffer-from-1.1.2.tgz}

  buffer-indexof@1.1.1:
    resolution: {integrity: sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=, tarball: http://npm.zhenguanyu.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz}

  buffer-xor@1.0.3:
    resolution: {integrity: sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=, tarball: http://npm.zhenguanyu.com/buffer-xor/download/buffer-xor-1.0.3.tgz}

  buffer@4.9.2:
    resolution: {integrity: sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=, tarball: http://npm.zhenguanyu.com/buffer/download/buffer-4.9.2.tgz}

  builtin-status-codes@3.0.0:
    resolution: {integrity: sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=, tarball: http://npm.zhenguanyu.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz}

  bytes@3.1.2:
    resolution: {integrity: sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=, tarball: http://npm.zhenguanyu.com/bytes/download/bytes-3.1.2.tgz}
    engines: {node: '>= 0.8'}

  cacache@10.0.4:
    resolution: {integrity: sha1-ZFI2eZnv+dQYiu/ZoU6dfGomNGA=, tarball: http://npm.zhenguanyu.com/cacache/download/cacache-10.0.4.tgz}

  cacache@12.0.4:
    resolution: {integrity: sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=, tarball: http://npm.zhenguanyu.com/cacache/download/cacache-12.0.4.tgz}

  cache-base@1.0.1:
    resolution: {integrity: sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=, tarball: http://npm.zhenguanyu.com/cache-base/download/cache-base-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  cache-loader@2.0.1:
    resolution: {integrity: sha1-V1j0GmLXwjlB48PHAW5vrrA6ywc=, tarball: http://npm.zhenguanyu.com/cache-loader/download/cache-loader-2.0.1.tgz}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0

  call-bind@1.0.7:
    resolution: {integrity: sha1-BgFlmcQMVkmMGHadJzC+JCtvo7k=, tarball: http://npm.zhenguanyu.com/call-bind/download/call-bind-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  call-me-maybe@1.0.2:
    resolution: {integrity: sha1-A/lk8ZUiumQ7GwaTrLkVL+IHS6o=, tarball: http://npm.zhenguanyu.com/call-me-maybe/download/call-me-maybe-1.0.2.tgz}

  caller-callsite@2.0.0:
    resolution: {integrity: sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=, tarball: http://npm.zhenguanyu.com/caller-callsite/download/caller-callsite-2.0.0.tgz}
    engines: {node: '>=4'}

  caller-path@0.1.0:
    resolution: {integrity: sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=, tarball: http://npm.zhenguanyu.com/caller-path/download/caller-path-0.1.0.tgz}
    engines: {node: '>=0.10.0'}

  caller-path@2.0.0:
    resolution: {integrity: sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=, tarball: http://npm.zhenguanyu.com/caller-path/download/caller-path-2.0.0.tgz}
    engines: {node: '>=4'}

  callsites@0.2.0:
    resolution: {integrity: sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=, tarball: http://npm.zhenguanyu.com/callsites/download/callsites-0.2.0.tgz}
    engines: {node: '>=0.10.0'}

  callsites@2.0.0:
    resolution: {integrity: sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=, tarball: http://npm.zhenguanyu.com/callsites/download/callsites-2.0.0.tgz}
    engines: {node: '>=4'}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=, tarball: http://npm.zhenguanyu.com/callsites/download/callsites-3.1.0.tgz}
    engines: {node: '>=6'}

  camel-case@3.0.0:
    resolution: {integrity: sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=, tarball: http://npm.zhenguanyu.com/camel-case/download/camel-case-3.0.0.tgz}

  camelcase@5.3.1:
    resolution: {integrity: sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=, tarball: http://npm.zhenguanyu.com/camelcase/download/camelcase-5.3.1.tgz}
    engines: {node: '>=6'}

  caniuse-api@3.0.0:
    resolution: {integrity: sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=, tarball: http://npm.zhenguanyu.com/caniuse-api/download/caniuse-api-3.0.0.tgz}

  caniuse-lite@1.0.30001676:
    resolution: {integrity: sha1-/hM9Qf50r498yTuKcUw+hqhubwQ=, tarball: http://npm.zhenguanyu.com/caniuse-lite/download/caniuse-lite-1.0.30001676.tgz}

  case-sensitive-paths-webpack-plugin@2.4.0:
    resolution: {integrity: sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ=, tarball: http://npm.zhenguanyu.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.4.0.tgz}
    engines: {node: '>=4'}

  caseless@0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=, tarball: http://npm.zhenguanyu.com/caseless/download/caseless-0.12.0.tgz}

  chalk@1.1.3:
    resolution: {integrity: sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=, tarball: http://npm.zhenguanyu.com/chalk/download/chalk-1.1.3.tgz}
    engines: {node: '>=0.10.0'}

  chalk@2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=, tarball: http://npm.zhenguanyu.com/chalk/download/chalk-2.4.2.tgz}
    engines: {node: '>=4'}

  chalk@3.0.0:
    resolution: {integrity: sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=, tarball: http://npm.zhenguanyu.com/chalk/download/chalk-3.0.0.tgz}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=, tarball: http://npm.zhenguanyu.com/chalk/download/chalk-4.1.2.tgz}
    engines: {node: '>=10'}

  chardet@0.4.2:
    resolution: {integrity: sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=, tarball: http://npm.zhenguanyu.com/chardet/download/chardet-0.4.2.tgz}

  chardet@0.7.0:
    resolution: {integrity: sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=, tarball: http://npm.zhenguanyu.com/chardet/download/chardet-0.7.0.tgz}

  check-types@8.0.3:
    resolution: {integrity: sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=, tarball: http://npm.zhenguanyu.com/check-types/download/check-types-8.0.3.tgz}

  chokidar@2.1.8:
    resolution: {integrity: sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=, tarball: http://npm.zhenguanyu.com/chokidar/download/chokidar-2.1.8.tgz}

  chokidar@3.6.0:
    resolution: {integrity: sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=, tarball: http://npm.zhenguanyu.com/chokidar/download/chokidar-3.6.0.tgz}
    engines: {node: '>= 8.10.0'}

  chownr@1.1.4:
    resolution: {integrity: sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=, tarball: http://npm.zhenguanyu.com/chownr/download/chownr-1.1.4.tgz}

  chrome-trace-event@1.0.4:
    resolution: {integrity: sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=, tarball: http://npm.zhenguanyu.com/chrome-trace-event/download/chrome-trace-event-1.0.4.tgz}
    engines: {node: '>=6.0'}

  cipher-base@1.0.4:
    resolution: {integrity: sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=, tarball: http://npm.zhenguanyu.com/cipher-base/download/cipher-base-1.0.4.tgz}

  circular-json@0.3.3:
    resolution: {integrity: sha1-gVyZ6oT2gJUp0vRXkb34JxE1LWY=, tarball: http://npm.zhenguanyu.com/circular-json/download/circular-json-0.3.3.tgz}
    deprecated: CircularJSON is in maintenance only, flatted is its successor.

  class-utils@0.3.6:
    resolution: {integrity: sha1-+TNprouafOAv1B+q0MqDAzGQxGM=, tarball: http://npm.zhenguanyu.com/class-utils/download/class-utils-0.3.6.tgz}
    engines: {node: '>=0.10.0'}

  clean-css@4.2.4:
    resolution: {integrity: sha1-czv0brpOYHxokepXwkqYk1aDEXg=, tarball: http://npm.zhenguanyu.com/clean-css/download/clean-css-4.2.4.tgz}
    engines: {node: '>= 4.0'}

  cli-cursor@2.1.0:
    resolution: {integrity: sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=, tarball: http://npm.zhenguanyu.com/cli-cursor/download/cli-cursor-2.1.0.tgz}
    engines: {node: '>=4'}

  cli-highlight@2.1.11:
    resolution: {integrity: sha1-SXNvpFLwqvT65YDjCssmgo0twb8=, tarball: http://npm.zhenguanyu.com/cli-highlight/download/cli-highlight-2.1.11.tgz}
    engines: {node: '>=8.0.0', npm: '>=5.0.0'}
    hasBin: true

  cli-spinners@2.9.2:
    resolution: {integrity: sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=, tarball: http://npm.zhenguanyu.com/cli-spinners/download/cli-spinners-2.9.2.tgz}
    engines: {node: '>=6'}

  cli-truncate@0.2.1:
    resolution: {integrity: sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=, tarball: http://npm.zhenguanyu.com/cli-truncate/download/cli-truncate-0.2.1.tgz}
    engines: {node: '>=0.10.0'}

  cli-width@2.2.1:
    resolution: {integrity: sha1-sEM9C06chH7xiGik7xb9X8gnHEg=, tarball: http://npm.zhenguanyu.com/cli-width/download/cli-width-2.2.1.tgz}

  clipboardy@2.3.0:
    resolution: {integrity: sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=, tarball: http://npm.zhenguanyu.com/clipboardy/download/clipboardy-2.3.0.tgz}
    engines: {node: '>=8'}

  cliui@5.0.0:
    resolution: {integrity: sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=, tarball: http://npm.zhenguanyu.com/cliui/download/cliui-5.0.0.tgz}

  cliui@6.0.0:
    resolution: {integrity: sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=, tarball: http://npm.zhenguanyu.com/cliui/download/cliui-6.0.0.tgz}

  cliui@7.0.4:
    resolution: {integrity: sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=, tarball: http://npm.zhenguanyu.com/cliui/download/cliui-7.0.4.tgz}

  clone@1.0.4:
    resolution: {integrity: sha1-2jCcwmPfFZlMaIypAheco8fNfH4=, tarball: http://npm.zhenguanyu.com/clone/download/clone-1.0.4.tgz}
    engines: {node: '>=0.8'}

  clone@2.1.2:
    resolution: {integrity: sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=, tarball: http://npm.zhenguanyu.com/clone/download/clone-2.1.2.tgz}
    engines: {node: '>=0.8'}

  co@4.6.0:
    resolution: {integrity: sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=, tarball: http://npm.zhenguanyu.com/co/download/co-4.6.0.tgz}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  coa@2.0.2:
    resolution: {integrity: sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=, tarball: http://npm.zhenguanyu.com/coa/download/coa-2.0.2.tgz}
    engines: {node: '>= 4.0'}

  code-point-at@1.1.0:
    resolution: {integrity: sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=, tarball: http://npm.zhenguanyu.com/code-point-at/download/code-point-at-1.1.0.tgz}
    engines: {node: '>=0.10.0'}

  collection-visit@1.0.0:
    resolution: {integrity: sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=, tarball: http://npm.zhenguanyu.com/collection-visit/download/collection-visit-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=, tarball: http://npm.zhenguanyu.com/color-convert/download/color-convert-1.9.3.tgz}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=, tarball: http://npm.zhenguanyu.com/color-convert/download/color-convert-2.0.1.tgz}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=, tarball: http://npm.zhenguanyu.com/color-name/download/color-name-1.1.3.tgz}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=, tarball: http://npm.zhenguanyu.com/color-name/download/color-name-1.1.4.tgz}

  color-string@1.9.1:
    resolution: {integrity: sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=, tarball: http://npm.zhenguanyu.com/color-string/download/color-string-1.9.1.tgz}

  color@3.2.1:
    resolution: {integrity: sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=, tarball: http://npm.zhenguanyu.com/color/download/color-3.2.1.tgz}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=, tarball: http://npm.zhenguanyu.com/combined-stream/download/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}

  commander@2.17.1:
    resolution: {integrity: sha1-vXerfebelCBc6sxy8XFtKfIKd78=, tarball: http://npm.zhenguanyu.com/commander/download/commander-2.17.1.tgz}

  commander@2.19.0:
    resolution: {integrity: sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=, tarball: http://npm.zhenguanyu.com/commander/download/commander-2.19.0.tgz}

  commander@2.20.3:
    resolution: {integrity: sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=, tarball: http://npm.zhenguanyu.com/commander/download/commander-2.20.3.tgz}

  comment-parser@0.5.5:
    resolution: {integrity: sha1-wlhMrnwvCvx3Ppay7pj4wQy9aT0=, tarball: http://npm.zhenguanyu.com/comment-parser/download/comment-parser-0.5.5.tgz}

  commondir@1.0.1:
    resolution: {integrity: sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=, tarball: http://npm.zhenguanyu.com/commondir/download/commondir-1.0.1.tgz}

  component-emitter@1.3.1:
    resolution: {integrity: sha1-7x1XlvfZPxNe5vtoQ0CyZAPJfRc=, tarball: http://npm.zhenguanyu.com/component-emitter/download/component-emitter-1.3.1.tgz}

  compressible@2.0.18:
    resolution: {integrity: sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=, tarball: http://npm.zhenguanyu.com/compressible/download/compressible-2.0.18.tgz}
    engines: {node: '>= 0.6'}

  compression@1.7.5:
    resolution: {integrity: sha1-/dJWwKZC454xTEePbCzWVO3XTJM=, tarball: http://npm.zhenguanyu.com/compression/download/compression-1.7.5.tgz}
    engines: {node: '>= 0.8.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: http://npm.zhenguanyu.com/concat-map/download/concat-map-0.0.1.tgz}

  concat-stream@1.6.2:
    resolution: {integrity: sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=, tarball: http://npm.zhenguanyu.com/concat-stream/download/concat-stream-1.6.2.tgz}
    engines: {'0': node >= 0.8}

  connect-history-api-fallback@1.6.0:
    resolution: {integrity: sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=, tarball: http://npm.zhenguanyu.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz}
    engines: {node: '>=0.8'}

  console-browserify@1.2.0:
    resolution: {integrity: sha1-ZwY871fOts9Jk6KrOlWECujEkzY=, tarball: http://npm.zhenguanyu.com/console-browserify/download/console-browserify-1.2.0.tgz}

  consolidate@0.15.1:
    resolution: {integrity: sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=, tarball: http://npm.zhenguanyu.com/consolidate/download/consolidate-0.15.1.tgz}
    engines: {node: '>= 0.10.0'}
    deprecated: Please upgrade to consolidate v1.0.0+ as it has been modernized with several long-awaited fixes implemented. Maintenance is supported by Forward Email at https://forwardemail.net ; follow/watch https://github.com/ladjs/consolidate for updates and release changelog
    peerDependencies:
      arc-templates: ^0.5.3
      atpl: '>=0.7.6'
      babel-core: ^6.26.3
      bracket-template: ^1.1.5
      coffee-script: ^1.12.7
      dot: ^1.1.3
      dust: ^0.3.0
      dustjs-helpers: ^1.7.4
      dustjs-linkedin: ^2.7.5
      eco: ^1.1.0-rc-3
      ect: ^0.5.9
      ejs: ^3.1.5
      haml-coffee: ^1.14.1
      hamlet: ^0.3.3
      hamljs: ^0.6.2
      handlebars: ^4.7.6
      hogan.js: ^3.0.2
      htmling: ^0.0.8
      jade: ^1.11.0
      jazz: ^0.0.18
      jqtpl: ~1.1.0
      just: ^0.1.8
      liquid-node: ^3.0.1
      liquor: ^0.0.5
      lodash: ^4.17.20
      marko: ^3.14.4
      mote: ^0.2.0
      mustache: ^3.0.0
      nunjucks: ^3.2.2
      plates: ~0.4.11
      pug: ^3.0.0
      qejs: ^3.0.5
      ractive: ^1.3.12
      razor-tmpl: ^1.3.1
      react: ^16.13.1
      react-dom: ^16.13.1
      slm: ^2.0.0
      squirrelly: ^5.1.0
      swig: ^1.4.2
      swig-templates: ^2.0.3
      teacup: ^2.0.0
      templayed: '>=0.2.3'
      then-jade: '*'
      then-pug: '*'
      tinyliquid: ^0.2.34
      toffee: ^0.3.6
      twig: ^1.15.2
      twing: ^5.0.2
      underscore: ^1.11.0
      vash: ^0.13.0
      velocityjs: ^2.0.1
      walrus: ^0.10.1
      whiskers: ^0.4.0
    peerDependenciesMeta:
      arc-templates:
        optional: true
      atpl:
        optional: true
      babel-core:
        optional: true
      bracket-template:
        optional: true
      coffee-script:
        optional: true
      dot:
        optional: true
      dust:
        optional: true
      dustjs-helpers:
        optional: true
      dustjs-linkedin:
        optional: true
      eco:
        optional: true
      ect:
        optional: true
      ejs:
        optional: true
      haml-coffee:
        optional: true
      hamlet:
        optional: true
      hamljs:
        optional: true
      handlebars:
        optional: true
      hogan.js:
        optional: true
      htmling:
        optional: true
      jade:
        optional: true
      jazz:
        optional: true
      jqtpl:
        optional: true
      just:
        optional: true
      liquid-node:
        optional: true
      liquor:
        optional: true
      lodash:
        optional: true
      marko:
        optional: true
      mote:
        optional: true
      mustache:
        optional: true
      nunjucks:
        optional: true
      plates:
        optional: true
      pug:
        optional: true
      qejs:
        optional: true
      ractive:
        optional: true
      razor-tmpl:
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      slm:
        optional: true
      squirrelly:
        optional: true
      swig:
        optional: true
      swig-templates:
        optional: true
      teacup:
        optional: true
      templayed:
        optional: true
      then-jade:
        optional: true
      then-pug:
        optional: true
      tinyliquid:
        optional: true
      toffee:
        optional: true
      twig:
        optional: true
      twing:
        optional: true
      underscore:
        optional: true
      vash:
        optional: true
      velocityjs:
        optional: true
      walrus:
        optional: true
      whiskers:
        optional: true

  constants-browserify@1.0.0:
    resolution: {integrity: sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=, tarball: http://npm.zhenguanyu.com/constants-browserify/download/constants-browserify-1.0.0.tgz}

  content-disposition@0.5.4:
    resolution: {integrity: sha1-i4K076yCUSoCuwsdzsnSxejrW/4=, tarball: http://npm.zhenguanyu.com/content-disposition/download/content-disposition-0.5.4.tgz}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=, tarball: http://npm.zhenguanyu.com/content-type/download/content-type-1.0.5.tgz}
    engines: {node: '>= 0.6'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=, tarball: http://npm.zhenguanyu.com/convert-source-map/download/convert-source-map-2.0.0.tgz}

  cookie-signature@1.0.6:
    resolution: {integrity: sha1-4wOogrNCzD7oylE6eZmXNNqzriw=, tarball: http://npm.zhenguanyu.com/cookie-signature/download/cookie-signature-1.0.6.tgz}

  cookie@0.7.1:
    resolution: {integrity: sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=, tarball: http://npm.zhenguanyu.com/cookie/download/cookie-0.7.1.tgz}
    engines: {node: '>= 0.6'}

  copy-anything@2.0.6:
    resolution: {integrity: sha1-CSRU6pWEp7etVXMGKyqH9ZAPxIA=, tarball: http://npm.zhenguanyu.com/copy-anything/download/copy-anything-2.0.6.tgz}

  copy-concurrently@1.0.5:
    resolution: {integrity: sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=, tarball: http://npm.zhenguanyu.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz}
    deprecated: This package is no longer supported.

  copy-descriptor@0.1.1:
    resolution: {integrity: sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=, tarball: http://npm.zhenguanyu.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  copy-webpack-plugin@4.6.0:
    resolution: {integrity: sha1-5/QN2KaEd9QF3Rt6hUquMksVi64=, tarball: http://npm.zhenguanyu.com/copy-webpack-plugin/download/copy-webpack-plugin-4.6.0.tgz}
    engines: {node: '>= 4'}

  core-js-compat@3.39.0:
    resolution: {integrity: sha1-sS3MtJXyYB3IYL2+e04/+oumP2E=, tarball: http://npm.zhenguanyu.com/core-js-compat/download/core-js-compat-3.39.0.tgz}

  core-js@2.6.12:
    resolution: {integrity: sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=, tarball: http://npm.zhenguanyu.com/core-js/download/core-js-2.6.12.tgz}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.

  core-util-is@1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=, tarball: http://npm.zhenguanyu.com/core-util-is/download/core-util-is-1.0.2.tgz}

  core-util-is@1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=, tarball: http://npm.zhenguanyu.com/core-util-is/download/core-util-is-1.0.3.tgz}

  cosmiconfig@5.2.1:
    resolution: {integrity: sha1-BA9yaAnFked6F8CjYmykW08Wixo=, tarball: http://npm.zhenguanyu.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz}
    engines: {node: '>=4'}

  create-ecdh@4.0.4:
    resolution: {integrity: sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=, tarball: http://npm.zhenguanyu.com/create-ecdh/download/create-ecdh-4.0.4.tgz}

  create-hash@1.2.0:
    resolution: {integrity: sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=, tarball: http://npm.zhenguanyu.com/create-hash/download/create-hash-1.2.0.tgz}

  create-hmac@1.1.7:
    resolution: {integrity: sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=, tarball: http://npm.zhenguanyu.com/create-hmac/download/create-hmac-1.1.7.tgz}

  cross-spawn@5.1.0:
    resolution: {integrity: sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=, tarball: http://npm.zhenguanyu.com/cross-spawn/download/cross-spawn-5.1.0.tgz}

  cross-spawn@6.0.5:
    resolution: {integrity: sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=, tarball: http://npm.zhenguanyu.com/cross-spawn/download/cross-spawn-6.0.5.tgz}
    engines: {node: '>=4.8'}

  cross-spawn@7.0.3:
    resolution: {integrity: sha1-9zqFudXUHQRVUcF34ogtSshXKKY=, tarball: http://npm.zhenguanyu.com/cross-spawn/download/cross-spawn-7.0.3.tgz}
    engines: {node: '>= 8'}

  crypto-browserify@3.12.1:
    resolution: {integrity: sha1-u4khvsmsyBYzN5qo9S1psLaeDaw=, tarball: http://npm.zhenguanyu.com/crypto-browserify/download/crypto-browserify-3.12.1.tgz}
    engines: {node: '>= 0.10'}

  css-color-names@0.0.4:
    resolution: {integrity: sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=, tarball: http://npm.zhenguanyu.com/css-color-names/download/css-color-names-0.0.4.tgz}

  css-declaration-sorter@4.0.1:
    resolution: {integrity: sha1-wZiUD2OnbX42wecQGLABchBUyyI=, tarball: http://npm.zhenguanyu.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz}
    engines: {node: '>4'}

  css-loader@1.0.1:
    resolution: {integrity: sha1-aIW7UjOzXsR7AGBX2gHMZAtref4=, tarball: http://npm.zhenguanyu.com/css-loader/download/css-loader-1.0.1.tgz}
    engines: {node: '>= 6.9.0 <7.0.0 || >= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0

  css-select-base-adapter@0.1.1:
    resolution: {integrity: sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=, tarball: http://npm.zhenguanyu.com/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz}

  css-select@2.1.0:
    resolution: {integrity: sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=, tarball: http://npm.zhenguanyu.com/css-select/download/css-select-2.1.0.tgz}

  css-select@4.3.0:
    resolution: {integrity: sha1-23EpsoRmYv2GKM/ElquytZ5BUps=, tarball: http://npm.zhenguanyu.com/css-select/download/css-select-4.3.0.tgz}

  css-selector-tokenizer@0.7.3:
    resolution: {integrity: sha1-c18mGG5nx0mq8nV4NAXPBmH66PE=, tarball: http://npm.zhenguanyu.com/css-selector-tokenizer/download/css-selector-tokenizer-0.7.3.tgz}

  css-tree@1.0.0-alpha.37:
    resolution: {integrity: sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=, tarball: http://npm.zhenguanyu.com/css-tree/download/css-tree-1.0.0-alpha.37.tgz}
    engines: {node: '>=8.0.0'}

  css-tree@1.1.3:
    resolution: {integrity: sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=, tarball: http://npm.zhenguanyu.com/css-tree/download/css-tree-1.1.3.tgz}
    engines: {node: '>=8.0.0'}

  css-what@3.4.2:
    resolution: {integrity: sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=, tarball: http://npm.zhenguanyu.com/css-what/download/css-what-3.4.2.tgz}
    engines: {node: '>= 6'}

  css-what@6.1.0:
    resolution: {integrity: sha1-+17/z3bx3eosgb36pN5E55uscPQ=, tarball: http://npm.zhenguanyu.com/css-what/download/css-what-6.1.0.tgz}
    engines: {node: '>= 6'}

  cssesc@3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=, tarball: http://npm.zhenguanyu.com/cssesc/download/cssesc-3.0.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  cssnano-preset-default@4.0.8:
    resolution: {integrity: sha1-kgYisfwelaNOiDggPxOXpQTy0/8=, tarball: http://npm.zhenguanyu.com/cssnano-preset-default/download/cssnano-preset-default-4.0.8.tgz}
    engines: {node: '>=6.9.0'}

  cssnano-util-get-arguments@4.0.0:
    resolution: {integrity: sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=, tarball: http://npm.zhenguanyu.com/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz}
    engines: {node: '>=6.9.0'}

  cssnano-util-get-match@4.0.0:
    resolution: {integrity: sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=, tarball: http://npm.zhenguanyu.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz}
    engines: {node: '>=6.9.0'}

  cssnano-util-raw-cache@4.0.1:
    resolution: {integrity: sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=, tarball: http://npm.zhenguanyu.com/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  cssnano-util-same-parent@4.0.1:
    resolution: {integrity: sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=, tarball: http://npm.zhenguanyu.com/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  cssnano@4.1.11:
    resolution: {integrity: sha1-x7X1uB2iacsf2YLLlgwSAJEMmpk=, tarball: http://npm.zhenguanyu.com/cssnano/download/cssnano-4.1.11.tgz}
    engines: {node: '>=6.9.0'}

  csso@4.2.0:
    resolution: {integrity: sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=, tarball: http://npm.zhenguanyu.com/csso/download/csso-4.2.0.tgz}
    engines: {node: '>=8.0.0'}

  csstype@3.1.3:
    resolution: {integrity: sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=, tarball: http://npm.zhenguanyu.com/csstype/download/csstype-3.1.3.tgz}

  current-script-polyfill@1.0.0:
    resolution: {integrity: sha1-8xz35PPiGLBybnOMqSoC00iO9hU=, tarball: http://npm.zhenguanyu.com/current-script-polyfill/download/current-script-polyfill-1.0.0.tgz}

  cyclist@1.0.2:
    resolution: {integrity: sha1-ZztfIzvzTY5gK5SUKfgXHZEhvqM=, tarball: http://npm.zhenguanyu.com/cyclist/download/cyclist-1.0.2.tgz}

  dashdash@1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=, tarball: http://npm.zhenguanyu.com/dashdash/download/dashdash-1.14.1.tgz}
    engines: {node: '>=0.10'}

  data-view-buffer@1.0.1:
    resolution: {integrity: sha1-jqYybv7Bei5CYgaW5nHX1ai8ZrI=, tarball: http://npm.zhenguanyu.com/data-view-buffer/download/data-view-buffer-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.1:
    resolution: {integrity: sha1-kHIcqV/ygGd+t5N0n84QETR2aeI=, tarball: http://npm.zhenguanyu.com/data-view-byte-length/download/data-view-byte-length-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.0:
    resolution: {integrity: sha1-Xgu/tIKO0tG5tADNin0Rm8oP8Yo=, tarball: http://npm.zhenguanyu.com/data-view-byte-offset/download/data-view-byte-offset-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  date-fns@1.30.1:
    resolution: {integrity: sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=, tarball: http://npm.zhenguanyu.com/date-fns/download/date-fns-1.30.1.tgz}

  de-indent@1.0.2:
    resolution: {integrity: sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=, tarball: http://npm.zhenguanyu.com/de-indent/download/de-indent-1.0.2.tgz}

  debug@2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=, tarball: http://npm.zhenguanyu.com/debug/download/debug-2.6.9.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.1.0:
    resolution: {integrity: sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=, tarball: http://npm.zhenguanyu.com/debug/download/debug-3.1.0.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution: {integrity: sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=, tarball: http://npm.zhenguanyu.com/debug/download/debug-3.2.7.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.7:
    resolution: {integrity: sha1-h5RbQVGgEddtlaGY1xEchlw2ClI=, tarball: http://npm.zhenguanyu.com/debug/download/debug-4.3.7.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution: {integrity: sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=, tarball: http://npm.zhenguanyu.com/decamelize/download/decamelize-1.2.0.tgz}
    engines: {node: '>=0.10.0'}

  decode-uri-component@0.2.2:
    resolution: {integrity: sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek=, tarball: http://npm.zhenguanyu.com/decode-uri-component/download/decode-uri-component-0.2.2.tgz}
    engines: {node: '>=0.10'}

  dedent@0.7.0:
    resolution: {integrity: sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=, tarball: http://npm.zhenguanyu.com/dedent/download/dedent-0.7.0.tgz}

  deep-equal@1.1.2:
    resolution: {integrity: sha1-eKVht4MO7zE0x/bzo9avJypnh2E=, tarball: http://npm.zhenguanyu.com/deep-equal/download/deep-equal-1.1.2.tgz}
    engines: {node: '>= 0.4'}

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=, tarball: http://npm.zhenguanyu.com/deep-is/download/deep-is-0.1.4.tgz}

  deepmerge@1.5.2:
    resolution: {integrity: sha1-EEmdhohEza1P7ghC34x/bwyVp1M=, tarball: http://npm.zhenguanyu.com/deepmerge/download/deepmerge-1.5.2.tgz}
    engines: {node: '>=0.10.0'}

  default-gateway@4.2.0:
    resolution: {integrity: sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=, tarball: http://npm.zhenguanyu.com/default-gateway/download/default-gateway-4.2.0.tgz}
    engines: {node: '>=6'}

  default-gateway@5.0.5:
    resolution: {integrity: sha1-T9a9XShV05s0zFpZUFSG6ar8mxA=, tarball: http://npm.zhenguanyu.com/default-gateway/download/default-gateway-5.0.5.tgz}
    engines: {node: ^8.12.0 || >=9.7.0}

  defaults@1.0.4:
    resolution: {integrity: sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=, tarball: http://npm.zhenguanyu.com/defaults/download/defaults-1.0.4.tgz}

  define-data-property@1.1.4:
    resolution: {integrity: sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=, tarball: http://npm.zhenguanyu.com/define-data-property/download/define-data-property-1.1.4.tgz}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=, tarball: http://npm.zhenguanyu.com/define-properties/download/define-properties-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  define-property@0.2.5:
    resolution: {integrity: sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=, tarball: http://npm.zhenguanyu.com/define-property/download/define-property-0.2.5.tgz}
    engines: {node: '>=0.10.0'}

  define-property@1.0.0:
    resolution: {integrity: sha1-dp66rz9KY6rTr56NMEybvnm/sOY=, tarball: http://npm.zhenguanyu.com/define-property/download/define-property-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  define-property@2.0.2:
    resolution: {integrity: sha1-1Flono1lS6d+AqgX+HENcCyxbp0=, tarball: http://npm.zhenguanyu.com/define-property/download/define-property-2.0.2.tgz}
    engines: {node: '>=0.10.0'}

  del@3.0.0:
    resolution: {integrity: sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=, tarball: http://npm.zhenguanyu.com/del/download/del-3.0.0.tgz}
    engines: {node: '>=4'}

  del@4.1.1:
    resolution: {integrity: sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=, tarball: http://npm.zhenguanyu.com/del/download/del-4.1.1.tgz}
    engines: {node: '>=6'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=, tarball: http://npm.zhenguanyu.com/delayed-stream/download/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}

  depd@1.1.2:
    resolution: {integrity: sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=, tarball: http://npm.zhenguanyu.com/depd/download/depd-1.1.2.tgz}
    engines: {node: '>= 0.6'}

  depd@2.0.0:
    resolution: {integrity: sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=, tarball: http://npm.zhenguanyu.com/depd/download/depd-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  des.js@1.1.0:
    resolution: {integrity: sha1-HTf1dm87v/Tuljjocah2jBc7gdo=, tarball: http://npm.zhenguanyu.com/des.js/download/des.js-1.1.0.tgz}

  destroy@1.2.0:
    resolution: {integrity: sha1-SANzVQmti+VSk0xn32FPlOZvoBU=, tarball: http://npm.zhenguanyu.com/destroy/download/destroy-1.2.0.tgz}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-node@2.1.0:
    resolution: {integrity: sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=, tarball: http://npm.zhenguanyu.com/detect-node/download/detect-node-2.1.0.tgz}

  diffie-hellman@5.0.3:
    resolution: {integrity: sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=, tarball: http://npm.zhenguanyu.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz}

  dir-glob@2.2.2:
    resolution: {integrity: sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=, tarball: http://npm.zhenguanyu.com/dir-glob/download/dir-glob-2.2.2.tgz}
    engines: {node: '>=4'}

  dns-equal@1.0.0:
    resolution: {integrity: sha1-s55/HabrCnW6nBcySzR1PEfgZU0=, tarball: http://npm.zhenguanyu.com/dns-equal/download/dns-equal-1.0.0.tgz}

  dns-packet@1.3.4:
    resolution: {integrity: sha1-40VQZYJKJQe6iGxVqJljuxB97G8=, tarball: http://npm.zhenguanyu.com/dns-packet/download/dns-packet-1.3.4.tgz}

  dns-txt@2.0.2:
    resolution: {integrity: sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=, tarball: http://npm.zhenguanyu.com/dns-txt/download/dns-txt-2.0.2.tgz}

  doctrine@2.1.0:
    resolution: {integrity: sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=, tarball: http://npm.zhenguanyu.com/doctrine/download/doctrine-2.1.0.tgz}
    engines: {node: '>=0.10.0'}

  doctrine@3.0.0:
    resolution: {integrity: sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=, tarball: http://npm.zhenguanyu.com/doctrine/download/doctrine-3.0.0.tgz}
    engines: {node: '>=6.0.0'}

  dom-converter@0.2.0:
    resolution: {integrity: sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=, tarball: http://npm.zhenguanyu.com/dom-converter/download/dom-converter-0.2.0.tgz}

  dom-serializer@0.2.2:
    resolution: {integrity: sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=, tarball: http://npm.zhenguanyu.com/dom-serializer/download/dom-serializer-0.2.2.tgz}

  dom-serializer@1.4.1:
    resolution: {integrity: sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=, tarball: http://npm.zhenguanyu.com/dom-serializer/download/dom-serializer-1.4.1.tgz}

  dom7@2.1.5:
    resolution: {integrity: sha1-p5QRAXgAsx2EAAcM2uu/ySwfY3c=, tarball: http://npm.zhenguanyu.com/dom7/download/dom7-2.1.5.tgz}

  domain-browser@1.2.0:
    resolution: {integrity: sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=, tarball: http://npm.zhenguanyu.com/domain-browser/download/domain-browser-1.2.0.tgz}
    engines: {node: '>=0.4', npm: '>=1.2'}

  domelementtype@1.3.1:
    resolution: {integrity: sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=, tarball: http://npm.zhenguanyu.com/domelementtype/download/domelementtype-1.3.1.tgz}

  domelementtype@2.3.0:
    resolution: {integrity: sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=, tarball: http://npm.zhenguanyu.com/domelementtype/download/domelementtype-2.3.0.tgz}

  domhandler@4.3.1:
    resolution: {integrity: sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=, tarball: http://npm.zhenguanyu.com/domhandler/download/domhandler-4.3.1.tgz}
    engines: {node: '>= 4'}

  domutils@1.7.0:
    resolution: {integrity: sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=, tarball: http://npm.zhenguanyu.com/domutils/download/domutils-1.7.0.tgz}

  domutils@2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=, tarball: http://npm.zhenguanyu.com/domutils/download/domutils-2.8.0.tgz}

  dot-prop@5.3.0:
    resolution: {integrity: sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=, tarball: http://npm.zhenguanyu.com/dot-prop/download/dot-prop-5.3.0.tgz}
    engines: {node: '>=8'}

  dotenv-expand@5.1.0:
    resolution: {integrity: sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=, tarball: http://npm.zhenguanyu.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz}

  dotenv@7.0.0:
    resolution: {integrity: sha1-or481Sc2ZzIG6KhftSEO6ilijnw=, tarball: http://npm.zhenguanyu.com/dotenv/download/dotenv-7.0.0.tgz}
    engines: {node: '>=6'}

  duplexer@0.1.2:
    resolution: {integrity: sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=, tarball: http://npm.zhenguanyu.com/duplexer/download/duplexer-0.1.2.tgz}

  duplexify@3.7.1:
    resolution: {integrity: sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=, tarball: http://npm.zhenguanyu.com/duplexify/download/duplexify-3.7.1.tgz}

  easy-stack@1.0.1:
    resolution: {integrity: sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY=, tarball: http://npm.zhenguanyu.com/easy-stack/download/easy-stack-1.0.1.tgz}
    engines: {node: '>=6.0.0'}

  ecc-jsbn@0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=, tarball: http://npm.zhenguanyu.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz}

  ee-first@1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=, tarball: http://npm.zhenguanyu.com/ee-first/download/ee-first-1.1.1.tgz}

  ejs@2.7.4:
    resolution: {integrity: sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=, tarball: http://npm.zhenguanyu.com/ejs/download/ejs-2.7.4.tgz}
    engines: {node: '>=0.10.0'}

  electron-to-chromium@1.5.50:
    resolution: {integrity: sha1-2bqBjaeyte8fPdMrznBG/rfpMjQ=, tarball: http://npm.zhenguanyu.com/electron-to-chromium/download/electron-to-chromium-1.5.50.tgz}

  elegant-spinner@1.0.1:
    resolution: {integrity: sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=, tarball: http://npm.zhenguanyu.com/elegant-spinner/download/elegant-spinner-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  elliptic@6.6.0:
    resolution: {integrity: sha1-WRnscjKGwe3yhoWqiSYdR2GvohA=, tarball: http://npm.zhenguanyu.com/elliptic/download/elliptic-6.6.0.tgz}

  emoji-regex@7.0.3:
    resolution: {integrity: sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=, tarball: http://npm.zhenguanyu.com/emoji-regex/download/emoji-regex-7.0.3.tgz}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=, tarball: http://npm.zhenguanyu.com/emoji-regex/download/emoji-regex-8.0.0.tgz}

  emojis-list@2.1.0:
    resolution: {integrity: sha1-TapNnbAPmBmIDHn6RXrlsJof04k=, tarball: http://npm.zhenguanyu.com/emojis-list/download/emojis-list-2.1.0.tgz}
    engines: {node: '>= 0.10'}

  emojis-list@3.0.0:
    resolution: {integrity: sha1-VXBmIEatKeLpFucariYKvf9Pang=, tarball: http://npm.zhenguanyu.com/emojis-list/download/emojis-list-3.0.0.tgz}
    engines: {node: '>= 4'}

  encodeurl@1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=, tarball: http://npm.zhenguanyu.com/encodeurl/download/encodeurl-1.0.2.tgz}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha1-e46omAd9fkCdOsRUdOo46vCFelg=, tarball: http://npm.zhenguanyu.com/encodeurl/download/encodeurl-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  end-of-stream@1.4.4:
    resolution: {integrity: sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=, tarball: http://npm.zhenguanyu.com/end-of-stream/download/end-of-stream-1.4.4.tgz}

  enhanced-resolve@4.5.0:
    resolution: {integrity: sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=, tarball: http://npm.zhenguanyu.com/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz}
    engines: {node: '>=6.9.0'}

  entities@2.2.0:
    resolution: {integrity: sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=, tarball: http://npm.zhenguanyu.com/entities/download/entities-2.2.0.tgz}

  errno@0.1.8:
    resolution: {integrity: sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=, tarball: http://npm.zhenguanyu.com/errno/download/errno-0.1.8.tgz}
    hasBin: true

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=, tarball: http://npm.zhenguanyu.com/error-ex/download/error-ex-1.3.2.tgz}

  error-stack-parser@2.1.4:
    resolution: {integrity: sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=, tarball: http://npm.zhenguanyu.com/error-stack-parser/download/error-stack-parser-2.1.4.tgz}

  es-abstract@1.23.3:
    resolution: {integrity: sha1-jwxaNc0hUxJXPFonyH39bIgaCqA=, tarball: http://npm.zhenguanyu.com/es-abstract/download/es-abstract-1.23.3.tgz}
    engines: {node: '>= 0.4'}

  es-array-method-boxes-properly@1.0.0:
    resolution: {integrity: sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=, tarball: http://npm.zhenguanyu.com/es-array-method-boxes-properly/download/es-array-method-boxes-properly-1.0.0.tgz}

  es-define-property@1.0.0:
    resolution: {integrity: sha1-x/rvvf+LJpbPX0aSHt+3fMS6OEU=, tarball: http://npm.zhenguanyu.com/es-define-property/download/es-define-property-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=, tarball: http://npm.zhenguanyu.com/es-errors/download/es-errors-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.0.0:
    resolution: {integrity: sha1-***************************=, tarball: http://npm.zhenguanyu.com/es-object-atoms/download/es-object-atoms-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.0.3:
    resolution: {integrity: sha1-i7YPCkQMLkKBliQoQ41YVFrzl3c=, tarball: http://npm.zhenguanyu.com/es-set-tostringtag/download/es-set-tostringtag-2.0.3.tgz}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.0.2:
    resolution: {integrity: sha1-H2lC5x7MeDXtHIqDAG2HcaY6N2M=, tarball: http://npm.zhenguanyu.com/es-shim-unscopables/download/es-shim-unscopables-1.0.2.tgz}

  es-to-primitive@1.2.1:
    resolution: {integrity: sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=, tarball: http://npm.zhenguanyu.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=, tarball: http://npm.zhenguanyu.com/escalade/download/escalade-3.2.0.tgz}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=, tarball: http://npm.zhenguanyu.com/escape-html/download/escape-html-1.0.3.tgz}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=, tarball: http://npm.zhenguanyu.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz}
    engines: {node: '>=0.8.0'}

  eslint-config-prettier@3.6.0:
    resolution: {integrity: sha1-jKP/rEvW7u9iOgZR+ddUkA4+whc=, tarball: http://npm.zhenguanyu.com/eslint-config-prettier/download/eslint-config-prettier-3.6.0.tgz}
    hasBin: true
    peerDependencies:
      eslint: '>=3.14.1'

  eslint-config-standard@12.0.0:
    resolution: {integrity: sha1-Y4tMZdsL1aQTGflruh8V3a0hB9k=, tarball: http://npm.zhenguanyu.com/eslint-config-standard/download/eslint-config-standard-12.0.0.tgz}
    peerDependencies:
      eslint: '>=5.0.0'
      eslint-plugin-import: '>=2.13.0'
      eslint-plugin-node: '>=7.0.0'
      eslint-plugin-promise: '>=4.0.0'
      eslint-plugin-standard: '>=4.0.0'

  eslint-import-resolver-node@0.3.9:
    resolution: {integrity: sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=, tarball: http://npm.zhenguanyu.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.9.tgz}

  eslint-loader@2.2.1:
    resolution: {integrity: sha1-KLnBLaVAV68IReKmEScBova/gzc=, tarball: http://npm.zhenguanyu.com/eslint-loader/download/eslint-loader-2.2.1.tgz}
    deprecated: This loader has been deprecated. Please use eslint-webpack-plugin
    peerDependencies:
      eslint: '>=1.6.0 <7.0.0'
      webpack: '>=2.0.0 <5.0.0'

  eslint-module-utils@2.12.0:
    resolution: {integrity: sha1-/kz7lI1h9JID17CIcZgrZbmvCws=, tarball: http://npm.zhenguanyu.com/eslint-module-utils/download/eslint-module-utils-2.12.0.tgz}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-es@1.4.1:
    resolution: {integrity: sha1-EqyuD0lT52ukRL/RsicQgaxiCZg=, tarball: http://npm.zhenguanyu.com/eslint-plugin-es/download/eslint-plugin-es-1.4.1.tgz}
    engines: {node: '>=6.5.0'}
    peerDependencies:
      eslint: '>=4.19.1'

  eslint-plugin-import@2.31.0:
    resolution: {integrity: sha1-MQzn5yDKHZwLs/aa39HGvdfZ4Oc=, tarball: http://npm.zhenguanyu.com/eslint-plugin-import/download/eslint-plugin-import-2.31.0.tgz}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-jsdoc@5.0.2:
    resolution: {integrity: sha1-M4ehFlEc4YgAhnC76lmN7B3hFUk=, tarball: http://npm.zhenguanyu.com/eslint-plugin-jsdoc/download/eslint-plugin-jsdoc-5.0.2.tgz}
    engines: {node: '>=6'}
    peerDependencies:
      eslint: '>=4.14.0'

  eslint-plugin-node@8.0.1:
    resolution: {integrity: sha1-Va41YAIoY9FB+noReZUyNApoWWQ=, tarball: http://npm.zhenguanyu.com/eslint-plugin-node/download/eslint-plugin-node-8.0.1.tgz}
    engines: {node: '>=6'}
    peerDependencies:
      eslint: '>=4.19.1'

  eslint-plugin-prettier@3.4.1:
    resolution: {integrity: sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=, tarball: http://npm.zhenguanyu.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz}
    engines: {node: '>=6.0.0'}
    peerDependencies:
      eslint: '>=5.0.0'
      eslint-config-prettier: '*'
      prettier: '>=1.13.0'
    peerDependenciesMeta:
      eslint-config-prettier:
        optional: true

  eslint-plugin-promise@4.3.1:
    resolution: {integrity: sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU=, tarball: http://npm.zhenguanyu.com/eslint-plugin-promise/download/eslint-plugin-promise-4.3.1.tgz}
    engines: {node: '>=6'}

  eslint-plugin-standard@4.1.0:
    resolution: {integrity: sha1-DDvzpn6FP4u7xYD7SUX78W9Bt8U=, tarball: http://npm.zhenguanyu.com/eslint-plugin-standard/download/eslint-plugin-standard-4.1.0.tgz}
    peerDependencies:
      eslint: '>=5.0.0'

  eslint-plugin-vue@4.7.1:
    resolution: {integrity: sha1-yCm5/GJYLBiXtaC5Sv1E7MpRHmM=, tarball: http://npm.zhenguanyu.com/eslint-plugin-vue/download/eslint-plugin-vue-4.7.1.tgz}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3.18.0 || ^4.0.0

  eslint-plugin-vue@5.2.3:
    resolution: {integrity: sha1-PudZfYI7VHiASy/rqYY7G3QnOWE=, tarball: http://npm.zhenguanyu.com/eslint-plugin-vue/download/eslint-plugin-vue-5.2.3.tgz}
    engines: {node: '>=6.5'}
    peerDependencies:
      eslint: ^5.0.0

  eslint-scope@3.7.3:
    resolution: {integrity: sha1-u1ByANPRf2AkdjYWC0gmKEsQhTU=, tarball: http://npm.zhenguanyu.com/eslint-scope/download/eslint-scope-3.7.3.tgz}
    engines: {node: '>=4.0.0'}

  eslint-scope@4.0.3:
    resolution: {integrity: sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=, tarball: http://npm.zhenguanyu.com/eslint-scope/download/eslint-scope-4.0.3.tgz}
    engines: {node: '>=4.0.0'}

  eslint-utils@1.4.3:
    resolution: {integrity: sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=, tarball: http://npm.zhenguanyu.com/eslint-utils/download/eslint-utils-1.4.3.tgz}
    engines: {node: '>=6'}

  eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=, tarball: http://npm.zhenguanyu.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz}
    engines: {node: '>=4'}

  eslint@4.19.1:
    resolution: {integrity: sha1-MtHWU+HZBAiFS/spbwdux+GGowA=, tarball: http://npm.zhenguanyu.com/eslint/download/eslint-4.19.1.tgz}
    engines: {node: '>=4'}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  eslint@5.16.0:
    resolution: {integrity: sha1-oeOsGq5KP72Clvz496tzFMu2q+o=, tarball: http://npm.zhenguanyu.com/eslint/download/eslint-5.16.0.tgz}
    engines: {node: ^6.14.0 || ^8.10.0 || >=9.10.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  espree@3.5.4:
    resolution: {integrity: sha1-sPRHGHyKi+2US4FaZgvd9d610ac=, tarball: http://npm.zhenguanyu.com/espree/download/espree-3.5.4.tgz}
    engines: {node: '>=0.10.0'}

  espree@4.1.0:
    resolution: {integrity: sha1-co1UUeD9FWwEOEp62J7VH/VOsl8=, tarball: http://npm.zhenguanyu.com/espree/download/espree-4.1.0.tgz}
    engines: {node: '>=6.0.0'}

  espree@5.0.1:
    resolution: {integrity: sha1-XWUm+k/H8HiKXPdbFfMDI+L4H3o=, tarball: http://npm.zhenguanyu.com/espree/download/espree-5.0.1.tgz}
    engines: {node: '>=6.0.0'}

  esprima@4.0.1:
    resolution: {integrity: sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=, tarball: http://npm.zhenguanyu.com/esprima/download/esprima-4.0.1.tgz}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=, tarball: http://npm.zhenguanyu.com/esquery/download/esquery-1.6.0.tgz}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=, tarball: http://npm.zhenguanyu.com/esrecurse/download/esrecurse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=, tarball: http://npm.zhenguanyu.com/estraverse/download/estraverse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=, tarball: http://npm.zhenguanyu.com/estraverse/download/estraverse-5.3.0.tgz}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=, tarball: http://npm.zhenguanyu.com/esutils/download/esutils-2.0.3.tgz}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=, tarball: http://npm.zhenguanyu.com/etag/download/etag-1.8.1.tgz}
    engines: {node: '>= 0.6'}

  event-pubsub@4.3.0:
    resolution: {integrity: sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=, tarball: http://npm.zhenguanyu.com/event-pubsub/download/event-pubsub-4.3.0.tgz}
    engines: {node: '>=4.0.0'}

  eventemitter3@4.0.7:
    resolution: {integrity: sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=, tarball: http://npm.zhenguanyu.com/eventemitter3/download/eventemitter3-4.0.7.tgz}

  events@3.3.0:
    resolution: {integrity: sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=, tarball: http://npm.zhenguanyu.com/events/download/events-3.3.0.tgz}
    engines: {node: '>=0.8.x'}

  eventsource@2.0.2:
    resolution: {integrity: sha1-dt/MApMPsv8zlSC20pDaVzqehQg=, tarball: http://npm.zhenguanyu.com/eventsource/download/eventsource-2.0.2.tgz}
    engines: {node: '>=12.0.0'}

  evp_bytestokey@1.0.3:
    resolution: {integrity: sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=, tarball: http://npm.zhenguanyu.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz}

  execa@1.0.0:
    resolution: {integrity: sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=, tarball: http://npm.zhenguanyu.com/execa/download/execa-1.0.0.tgz}
    engines: {node: '>=6'}

  execa@3.4.0:
    resolution: {integrity: sha1-wI7UVQ72XYWPrCaf/IVyRG8364k=, tarball: http://npm.zhenguanyu.com/execa/download/execa-3.4.0.tgz}
    engines: {node: ^8.12.0 || >=9.7.0}

  expand-brackets@2.1.4:
    resolution: {integrity: sha1-t3c14xXOMPa27/D4OwQVGiJEliI=, tarball: http://npm.zhenguanyu.com/expand-brackets/download/expand-brackets-2.1.4.tgz}
    engines: {node: '>=0.10.0'}

  express@4.21.1:
    resolution: {integrity: sha1-na5d2oMvFrTuyUGk5EqonsSBsoE=, tarball: http://npm.zhenguanyu.com/express/download/express-4.21.1.tgz}
    engines: {node: '>= 0.10.0'}

  extend-shallow@2.0.1:
    resolution: {integrity: sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=, tarball: http://npm.zhenguanyu.com/extend-shallow/download/extend-shallow-2.0.1.tgz}
    engines: {node: '>=0.10.0'}

  extend-shallow@3.0.2:
    resolution: {integrity: sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=, tarball: http://npm.zhenguanyu.com/extend-shallow/download/extend-shallow-3.0.2.tgz}
    engines: {node: '>=0.10.0'}

  extend@3.0.2:
    resolution: {integrity: sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=, tarball: http://npm.zhenguanyu.com/extend/download/extend-3.0.2.tgz}

  external-editor@2.2.0:
    resolution: {integrity: sha1-BFURz9jRM/OEZnPRBHwVTiFK09U=, tarball: http://npm.zhenguanyu.com/external-editor/download/external-editor-2.2.0.tgz}
    engines: {node: '>=0.12'}

  external-editor@3.1.0:
    resolution: {integrity: sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=, tarball: http://npm.zhenguanyu.com/external-editor/download/external-editor-3.1.0.tgz}
    engines: {node: '>=4'}

  extglob@2.0.4:
    resolution: {integrity: sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=, tarball: http://npm.zhenguanyu.com/extglob/download/extglob-2.0.4.tgz}
    engines: {node: '>=0.10.0'}

  extsprintf@1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=, tarball: http://npm.zhenguanyu.com/extsprintf/download/extsprintf-1.3.0.tgz}
    engines: {'0': node >=0.6.0}

  fast-deep-equal@1.1.0:
    resolution: {integrity: sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ=, tarball: http://npm.zhenguanyu.com/fast-deep-equal/download/fast-deep-equal-1.1.0.tgz}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=, tarball: http://npm.zhenguanyu.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz}

  fast-diff@1.3.0:
    resolution: {integrity: sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=, tarball: http://npm.zhenguanyu.com/fast-diff/download/fast-diff-1.3.0.tgz}

  fast-glob@2.2.7:
    resolution: {integrity: sha1-aVOFfDr6R1//ku5gFdUtpwpM050=, tarball: http://npm.zhenguanyu.com/fast-glob/download/fast-glob-2.2.7.tgz}
    engines: {node: '>=4.0.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=, tarball: http://npm.zhenguanyu.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=, tarball: http://npm.zhenguanyu.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz}

  fastparse@1.1.2:
    resolution: {integrity: sha1-kXKMWllC7O2FMSg8eUQe5BIsNak=, tarball: http://npm.zhenguanyu.com/fastparse/download/fastparse-1.1.2.tgz}

  faye-websocket@0.11.4:
    resolution: {integrity: sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=, tarball: http://npm.zhenguanyu.com/faye-websocket/download/faye-websocket-0.11.4.tgz}
    engines: {node: '>=0.8.0'}

  figgy-pudding@3.5.2:
    resolution: {integrity: sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=, tarball: http://npm.zhenguanyu.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz}
    deprecated: This module is no longer supported.

  figures@1.7.0:
    resolution: {integrity: sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=, tarball: http://npm.zhenguanyu.com/figures/download/figures-1.7.0.tgz}
    engines: {node: '>=0.10.0'}

  figures@2.0.0:
    resolution: {integrity: sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=, tarball: http://npm.zhenguanyu.com/figures/download/figures-2.0.0.tgz}
    engines: {node: '>=4'}

  file-entry-cache@2.0.0:
    resolution: {integrity: sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=, tarball: http://npm.zhenguanyu.com/file-entry-cache/download/file-entry-cache-2.0.0.tgz}
    engines: {node: '>=0.10.0'}

  file-entry-cache@5.0.1:
    resolution: {integrity: sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=, tarball: http://npm.zhenguanyu.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz}
    engines: {node: '>=4'}

  file-loader@3.0.1:
    resolution: {integrity: sha1-+OC6C1mZGLUa3+RdZtHnca1WD6o=, tarball: http://npm.zhenguanyu.com/file-loader/download/file-loader-3.0.1.tgz}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0

  file-uri-to-path@1.0.0:
    resolution: {integrity: sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=, tarball: http://npm.zhenguanyu.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz}

  filesize@3.6.1:
    resolution: {integrity: sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=, tarball: http://npm.zhenguanyu.com/filesize/download/filesize-3.6.1.tgz}
    engines: {node: '>= 0.4.0'}

  fill-range@4.0.0:
    resolution: {integrity: sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=, tarball: http://npm.zhenguanyu.com/fill-range/download/fill-range-4.0.0.tgz}
    engines: {node: '>=0.10.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=, tarball: http://npm.zhenguanyu.com/fill-range/download/fill-range-7.1.1.tgz}
    engines: {node: '>=8'}

  finalhandler@1.3.1:
    resolution: {integrity: sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=, tarball: http://npm.zhenguanyu.com/finalhandler/download/finalhandler-1.3.1.tgz}
    engines: {node: '>= 0.8'}

  find-babel-config@1.2.2:
    resolution: {integrity: sha1-QRmbXLkVTcsv3DUcvnDq+RmNURE=, tarball: http://npm.zhenguanyu.com/find-babel-config/download/find-babel-config-1.2.2.tgz}
    engines: {node: '>=4.0.0'}

  find-cache-dir@0.1.1:
    resolution: {integrity: sha1-yN765XyKUqinhPnjHFfHQumToLk=, tarball: http://npm.zhenguanyu.com/find-cache-dir/download/find-cache-dir-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  find-cache-dir@1.0.0:
    resolution: {integrity: sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=, tarball: http://npm.zhenguanyu.com/find-cache-dir/download/find-cache-dir-1.0.0.tgz}
    engines: {node: '>=4'}

  find-cache-dir@2.1.0:
    resolution: {integrity: sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=, tarball: http://npm.zhenguanyu.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz}
    engines: {node: '>=6'}

  find-cache-dir@3.3.2:
    resolution: {integrity: sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=, tarball: http://npm.zhenguanyu.com/find-cache-dir/download/find-cache-dir-3.3.2.tgz}
    engines: {node: '>=8'}

  find-up@1.1.2:
    resolution: {integrity: sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=, tarball: http://npm.zhenguanyu.com/find-up/download/find-up-1.1.2.tgz}
    engines: {node: '>=0.10.0'}

  find-up@2.1.0:
    resolution: {integrity: sha1-RdG35QbHF93UgndaK3eSCjwMV6c=, tarball: http://npm.zhenguanyu.com/find-up/download/find-up-2.1.0.tgz}
    engines: {node: '>=4'}

  find-up@3.0.0:
    resolution: {integrity: sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=, tarball: http://npm.zhenguanyu.com/find-up/download/find-up-3.0.0.tgz}
    engines: {node: '>=6'}

  find-up@4.1.0:
    resolution: {integrity: sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=, tarball: http://npm.zhenguanyu.com/find-up/download/find-up-4.1.0.tgz}
    engines: {node: '>=8'}

  flat-cache@1.3.4:
    resolution: {integrity: sha1-LC73dSXMKSkAff/6HdMUqpyd7m8=, tarball: http://npm.zhenguanyu.com/flat-cache/download/flat-cache-1.3.4.tgz}
    engines: {node: '>=0.10.0'}

  flat-cache@2.0.1:
    resolution: {integrity: sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=, tarball: http://npm.zhenguanyu.com/flat-cache/download/flat-cache-2.0.1.tgz}
    engines: {node: '>=4'}

  flatted@2.0.2:
    resolution: {integrity: sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=, tarball: http://npm.zhenguanyu.com/flatted/download/flatted-2.0.2.tgz}

  flush-write-stream@1.1.1:
    resolution: {integrity: sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=, tarball: http://npm.zhenguanyu.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz}

  fn-name@2.0.1:
    resolution: {integrity: sha1-UhTXU3pNBqSjAcDMJi/rhBiAAuc=, tarball: http://npm.zhenguanyu.com/fn-name/download/fn-name-2.0.1.tgz}
    engines: {node: '>=0.10.0'}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=, tarball: http://npm.zhenguanyu.com/follow-redirects/download/follow-redirects-1.15.9.tgz}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  follow-redirects@1.5.10:
    resolution: {integrity: sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=, tarball: http://npm.zhenguanyu.com/follow-redirects/download/follow-redirects-1.5.10.tgz}
    engines: {node: '>=4.0'}

  for-each@0.3.3:
    resolution: {integrity: sha1-abRH6IoKXTLD5whPPxcQA0shN24=, tarball: http://npm.zhenguanyu.com/for-each/download/for-each-0.3.3.tgz}

  for-in@1.0.2:
    resolution: {integrity: sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=, tarball: http://npm.zhenguanyu.com/for-in/download/for-in-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  forever-agent@0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=, tarball: http://npm.zhenguanyu.com/forever-agent/download/forever-agent-0.6.1.tgz}

  form-data@2.3.3:
    resolution: {integrity: sha1-3M5SwF9kTymManq5Nr1yTO/786Y=, tarball: http://npm.zhenguanyu.com/form-data/download/form-data-2.3.3.tgz}
    engines: {node: '>= 0.12'}

  forwarded@0.2.0:
    resolution: {integrity: sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=, tarball: http://npm.zhenguanyu.com/forwarded/download/forwarded-0.2.0.tgz}
    engines: {node: '>= 0.6'}

  fragment-cache@0.2.1:
    resolution: {integrity: sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=, tarball: http://npm.zhenguanyu.com/fragment-cache/download/fragment-cache-0.2.1.tgz}
    engines: {node: '>=0.10.0'}

  fresh@0.5.2:
    resolution: {integrity: sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=, tarball: http://npm.zhenguanyu.com/fresh/download/fresh-0.5.2.tgz}
    engines: {node: '>= 0.6'}

  from2@2.3.0:
    resolution: {integrity: sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=, tarball: http://npm.zhenguanyu.com/from2/download/from2-2.3.0.tgz}

  fs-extra@7.0.1:
    resolution: {integrity: sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=, tarball: http://npm.zhenguanyu.com/fs-extra/download/fs-extra-7.0.1.tgz}
    engines: {node: '>=6 <7 || >=8'}

  fs-write-stream-atomic@1.0.10:
    resolution: {integrity: sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=, tarball: http://npm.zhenguanyu.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz}
    deprecated: This package is no longer supported.

  fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=, tarball: http://npm.zhenguanyu.com/fs.realpath/download/fs.realpath-1.0.0.tgz}

  fsevents@1.2.13:
    resolution: {integrity: sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=, tarball: http://npm.zhenguanyu.com/fsevents/download/fsevents-1.2.13.tgz}
    engines: {node: '>= 4.0'}
    deprecated: Upgrade to fsevents v2 to mitigate potential security issues

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=, tarball: http://npm.zhenguanyu.com/fsevents/download/fsevents-2.3.3.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=, tarball: http://npm.zhenguanyu.com/function-bind/download/function-bind-1.1.2.tgz}

  function.prototype.name@1.1.6:
    resolution: {integrity: sha1-zfMVt9kO53pMbuIWw8M2LaB1M/0=, tarball: http://npm.zhenguanyu.com/function.prototype.name/download/function.prototype.name-1.1.6.tgz}
    engines: {node: '>= 0.4'}

  functional-red-black-tree@1.0.1:
    resolution: {integrity: sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=, tarball: http://npm.zhenguanyu.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz}

  functions-have-names@1.2.3:
    resolution: {integrity: sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=, tarball: http://npm.zhenguanyu.com/functions-have-names/download/functions-have-names-1.2.3.tgz}

  g-status@2.0.2:
    resolution: {integrity: sha1-Jw/TIRno/JSW8Gb+X+iOCmvHi5c=, tarball: http://npm.zhenguanyu.com/g-status/download/g-status-2.0.2.tgz}
    engines: {node: '>=6'}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=, tarball: http://npm.zhenguanyu.com/gensync/download/gensync-1.0.0-beta.2.tgz}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=, tarball: http://npm.zhenguanyu.com/get-caller-file/download/get-caller-file-2.0.5.tgz}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.2.4:
    resolution: {integrity: sha1-44X1pLUifUScPqu60FSU7wq76t0=, tarball: http://npm.zhenguanyu.com/get-intrinsic/download/get-intrinsic-1.2.4.tgz}
    engines: {node: '>= 0.4'}

  get-own-enumerable-property-symbols@3.0.2:
    resolution: {integrity: sha1-tf3nfyLL4185C04ImSLFC85u9mQ=, tarball: http://npm.zhenguanyu.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz}

  get-stdin@6.0.0:
    resolution: {integrity: sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=, tarball: http://npm.zhenguanyu.com/get-stdin/download/get-stdin-6.0.0.tgz}
    engines: {node: '>=4'}

  get-stream@4.1.0:
    resolution: {integrity: sha1-wbJVV189wh1Zv8ec09K0axw6VLU=, tarball: http://npm.zhenguanyu.com/get-stream/download/get-stream-4.1.0.tgz}
    engines: {node: '>=6'}

  get-stream@5.2.0:
    resolution: {integrity: sha1-SWaheV7lrOZecGxLe+txJX1uItM=, tarball: http://npm.zhenguanyu.com/get-stream/download/get-stream-5.2.0.tgz}
    engines: {node: '>=8'}

  get-symbol-description@1.0.2:
    resolution: {integrity: sha1-UzdE1aogrKTgecjl2vf9RCAoIfU=, tarball: http://npm.zhenguanyu.com/get-symbol-description/download/get-symbol-description-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  get-value@2.0.6:
    resolution: {integrity: sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=, tarball: http://npm.zhenguanyu.com/get-value/download/get-value-2.0.6.tgz}
    engines: {node: '>=0.10.0'}

  getpass@0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=, tarball: http://npm.zhenguanyu.com/getpass/download/getpass-0.1.7.tgz}

  glob-parent@3.1.0:
    resolution: {integrity: sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=, tarball: http://npm.zhenguanyu.com/glob-parent/download/glob-parent-3.1.0.tgz}

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=, tarball: http://npm.zhenguanyu.com/glob-parent/download/glob-parent-5.1.2.tgz}
    engines: {node: '>= 6'}

  glob-to-regexp@0.3.0:
    resolution: {integrity: sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=, tarball: http://npm.zhenguanyu.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz}

  glob@7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=, tarball: http://npm.zhenguanyu.com/glob/download/glob-7.2.3.tgz}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=, tarball: http://npm.zhenguanyu.com/globals/download/globals-11.12.0.tgz}
    engines: {node: '>=4'}

  globalthis@1.0.4:
    resolution: {integrity: sha1-dDDtOpddl7+1m8zkH1yruvplEjY=, tarball: http://npm.zhenguanyu.com/globalthis/download/globalthis-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  globby@6.1.0:
    resolution: {integrity: sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=, tarball: http://npm.zhenguanyu.com/globby/download/globby-6.1.0.tgz}
    engines: {node: '>=0.10.0'}

  globby@7.1.1:
    resolution: {integrity: sha1-+yzP+UAfhgCUXfral0QMypcrhoA=, tarball: http://npm.zhenguanyu.com/globby/download/globby-7.1.1.tgz}
    engines: {node: '>=4'}

  globby@9.2.0:
    resolution: {integrity: sha1-/QKacGxwPSm90XD0tts6P3p8tj0=, tarball: http://npm.zhenguanyu.com/globby/download/globby-9.2.0.tgz}
    engines: {node: '>=6'}

  gopd@1.0.1:
    resolution: {integrity: sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=, tarball: http://npm.zhenguanyu.com/gopd/download/gopd-1.0.1.tgz}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=, tarball: http://npm.zhenguanyu.com/graceful-fs/download/graceful-fs-4.2.11.tgz}

  gzip-size@5.1.1:
    resolution: {integrity: sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=, tarball: http://npm.zhenguanyu.com/gzip-size/download/gzip-size-5.1.1.tgz}
    engines: {node: '>=6'}

  handle-thing@2.0.1:
    resolution: {integrity: sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=, tarball: http://npm.zhenguanyu.com/handle-thing/download/handle-thing-2.0.1.tgz}

  har-schema@2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=, tarball: http://npm.zhenguanyu.com/har-schema/download/har-schema-2.0.0.tgz}
    engines: {node: '>=4'}

  har-validator@5.1.5:
    resolution: {integrity: sha1-HwgDufjLIMD6E4It8ezds2veHv0=, tarball: http://npm.zhenguanyu.com/har-validator/download/har-validator-5.1.5.tgz}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported

  has-ansi@2.0.0:
    resolution: {integrity: sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=, tarball: http://npm.zhenguanyu.com/has-ansi/download/has-ansi-2.0.0.tgz}
    engines: {node: '>=0.10.0'}

  has-bigints@1.0.2:
    resolution: {integrity: sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=, tarball: http://npm.zhenguanyu.com/has-bigints/download/has-bigints-1.0.2.tgz}

  has-flag@3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=, tarball: http://npm.zhenguanyu.com/has-flag/download/has-flag-3.0.0.tgz}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=, tarball: http://npm.zhenguanyu.com/has-flag/download/has-flag-4.0.0.tgz}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=, tarball: http://npm.zhenguanyu.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz}

  has-proto@1.0.3:
    resolution: {integrity: sha1-sx3f6bDm6ZFFNqarKGQm0CFPd/0=, tarball: http://npm.zhenguanyu.com/has-proto/download/has-proto-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=, tarball: http://npm.zhenguanyu.com/has-symbols/download/has-symbols-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=, tarball: http://npm.zhenguanyu.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  has-value@0.3.1:
    resolution: {integrity: sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=, tarball: http://npm.zhenguanyu.com/has-value/download/has-value-0.3.1.tgz}
    engines: {node: '>=0.10.0'}

  has-value@1.0.0:
    resolution: {integrity: sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=, tarball: http://npm.zhenguanyu.com/has-value/download/has-value-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  has-values@0.1.4:
    resolution: {integrity: sha1-bWHeldkd/Km5oCCJrThL/49it3E=, tarball: http://npm.zhenguanyu.com/has-values/download/has-values-0.1.4.tgz}
    engines: {node: '>=0.10.0'}

  has-values@1.0.0:
    resolution: {integrity: sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=, tarball: http://npm.zhenguanyu.com/has-values/download/has-values-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  has@1.0.4:
    resolution: {integrity: sha1-LrKGDgAAEdrk8UBqhv6A5TD7LsY=, tarball: http://npm.zhenguanyu.com/has/download/has-1.0.4.tgz}
    engines: {node: '>= 0.4.0'}

  hash-base@3.0.4:
    resolution: {integrity: sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=, tarball: http://npm.zhenguanyu.com/hash-base/download/hash-base-3.0.4.tgz}
    engines: {node: '>=4'}

  hash-sum@1.0.2:
    resolution: {integrity: sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=, tarball: http://npm.zhenguanyu.com/hash-sum/download/hash-sum-1.0.2.tgz}

  hash.js@1.1.7:
    resolution: {integrity: sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=, tarball: http://npm.zhenguanyu.com/hash.js/download/hash.js-1.1.7.tgz}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=, tarball: http://npm.zhenguanyu.com/hasown/download/hasown-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=, tarball: http://npm.zhenguanyu.com/he/download/he-1.2.0.tgz}
    hasBin: true

  hex-color-regex@1.1.0:
    resolution: {integrity: sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=, tarball: http://npm.zhenguanyu.com/hex-color-regex/download/hex-color-regex-1.1.0.tgz}

  highlight.js@10.7.3:
    resolution: {integrity: sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE=, tarball: http://npm.zhenguanyu.com/highlight.js/download/highlight.js-10.7.3.tgz}

  hmac-drbg@1.0.1:
    resolution: {integrity: sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=, tarball: http://npm.zhenguanyu.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz}

  hoopy@0.1.4:
    resolution: {integrity: sha1-YJIH1mEQADOpqUAq096mdzgcGx0=, tarball: http://npm.zhenguanyu.com/hoopy/download/hoopy-0.1.4.tgz}
    engines: {node: '>= 6.0.0'}

  hosted-git-info@2.8.9:
    resolution: {integrity: sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=, tarball: http://npm.zhenguanyu.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz}

  hpack.js@2.1.6:
    resolution: {integrity: sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=, tarball: http://npm.zhenguanyu.com/hpack.js/download/hpack.js-2.1.6.tgz}

  hsl-regex@1.0.0:
    resolution: {integrity: sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=, tarball: http://npm.zhenguanyu.com/hsl-regex/download/hsl-regex-1.0.0.tgz}

  hsla-regex@1.0.0:
    resolution: {integrity: sha1-wc56MWjIxmFAM6S194d/OyJfnDg=, tarball: http://npm.zhenguanyu.com/hsla-regex/download/hsla-regex-1.0.0.tgz}

  html-entities@1.4.0:
    resolution: {integrity: sha1-z70bAdKvr5rcobEK59/6uYxx0tw=, tarball: http://npm.zhenguanyu.com/html-entities/download/html-entities-1.4.0.tgz}

  html-minifier@3.5.21:
    resolution: {integrity: sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=, tarball: http://npm.zhenguanyu.com/html-minifier/download/html-minifier-3.5.21.tgz}
    engines: {node: '>=4'}
    hasBin: true

  html-tags@2.0.0:
    resolution: {integrity: sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=, tarball: http://npm.zhenguanyu.com/html-tags/download/html-tags-2.0.0.tgz}
    engines: {node: '>=4'}

  html-webpack-plugin@3.2.0:
    resolution: {integrity: sha1-sBq71yOsqqeze2r0SS69oD2d03s=, tarball: http://npm.zhenguanyu.com/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz}
    engines: {node: '>=6.9'}
    deprecated: 3.x is no longer supported
    peerDependencies:
      webpack: ^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0

  htmlparser2@6.1.0:
    resolution: {integrity: sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=, tarball: http://npm.zhenguanyu.com/htmlparser2/download/htmlparser2-6.1.0.tgz}

  http-deceiver@1.2.7:
    resolution: {integrity: sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=, tarball: http://npm.zhenguanyu.com/http-deceiver/download/http-deceiver-1.2.7.tgz}

  http-errors@1.6.3:
    resolution: {integrity: sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=, tarball: http://npm.zhenguanyu.com/http-errors/download/http-errors-1.6.3.tgz}
    engines: {node: '>= 0.6'}

  http-errors@2.0.0:
    resolution: {integrity: sha1-t3dKFIbvc892Z6ya4IWMASxXudM=, tarball: http://npm.zhenguanyu.com/http-errors/download/http-errors-2.0.0.tgz}
    engines: {node: '>= 0.8'}

  http-parser-js@0.5.8:
    resolution: {integrity: sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM=, tarball: http://npm.zhenguanyu.com/http-parser-js/download/http-parser-js-0.5.8.tgz}

  http-proxy-middleware@0.19.1:
    resolution: {integrity: sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=, tarball: http://npm.zhenguanyu.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz}
    engines: {node: '>=4.0.0'}

  http-proxy@1.18.1:
    resolution: {integrity: sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=, tarball: http://npm.zhenguanyu.com/http-proxy/download/http-proxy-1.18.1.tgz}
    engines: {node: '>=8.0.0'}

  http-signature@1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=, tarball: http://npm.zhenguanyu.com/http-signature/download/http-signature-1.2.0.tgz}
    engines: {node: '>=0.8', npm: '>=1.3.7'}

  https-browserify@1.0.0:
    resolution: {integrity: sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=, tarball: http://npm.zhenguanyu.com/https-browserify/download/https-browserify-1.0.0.tgz}

  human-signals@1.1.1:
    resolution: {integrity: sha1-xbHNFPUK6uCatsWf5jujOV/k36M=, tarball: http://npm.zhenguanyu.com/human-signals/download/human-signals-1.1.1.tgz}
    engines: {node: '>=8.12.0'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=, tarball: http://npm.zhenguanyu.com/iconv-lite/download/iconv-lite-0.4.24.tgz}
    engines: {node: '>=0.10.0'}

  icss-replace-symbols@1.1.0:
    resolution: {integrity: sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=, tarball: http://npm.zhenguanyu.com/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz}

  icss-utils@2.1.0:
    resolution: {integrity: sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI=, tarball: http://npm.zhenguanyu.com/icss-utils/download/icss-utils-2.1.0.tgz}

  ieee754@1.2.1:
    resolution: {integrity: sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=, tarball: http://npm.zhenguanyu.com/ieee754/download/ieee754-1.2.1.tgz}

  iferr@0.1.5:
    resolution: {integrity: sha1-xg7taebY/bazEEofy8ocGS3FtQE=, tarball: http://npm.zhenguanyu.com/iferr/download/iferr-0.1.5.tgz}

  ignore@3.3.10:
    resolution: {integrity: sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=, tarball: http://npm.zhenguanyu.com/ignore/download/ignore-3.3.10.tgz}

  ignore@4.0.6:
    resolution: {integrity: sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=, tarball: http://npm.zhenguanyu.com/ignore/download/ignore-4.0.6.tgz}
    engines: {node: '>= 4'}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=, tarball: http://npm.zhenguanyu.com/ignore/download/ignore-5.3.2.tgz}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=, tarball: http://npm.zhenguanyu.com/image-size/download/image-size-0.5.5.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  import-cwd@2.1.0:
    resolution: {integrity: sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=, tarball: http://npm.zhenguanyu.com/import-cwd/download/import-cwd-2.1.0.tgz}
    engines: {node: '>=4'}

  import-fresh@2.0.0:
    resolution: {integrity: sha1-2BNVwVYS04bGH53dOSLUMEgipUY=, tarball: http://npm.zhenguanyu.com/import-fresh/download/import-fresh-2.0.0.tgz}
    engines: {node: '>=4'}

  import-fresh@3.3.0:
    resolution: {integrity: sha1-NxYsJfy566oublPVtNiM4X2eDCs=, tarball: http://npm.zhenguanyu.com/import-fresh/download/import-fresh-3.3.0.tgz}
    engines: {node: '>=6'}

  import-from@2.1.0:
    resolution: {integrity: sha1-M1238qev/VOqpHHUuAId7ja387E=, tarball: http://npm.zhenguanyu.com/import-from/download/import-from-2.1.0.tgz}
    engines: {node: '>=4'}

  import-local@2.0.0:
    resolution: {integrity: sha1-VQcL44pZk88Y72236WH1vuXFoJ0=, tarball: http://npm.zhenguanyu.com/import-local/download/import-local-2.0.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=, tarball: http://npm.zhenguanyu.com/imurmurhash/download/imurmurhash-0.1.4.tgz}
    engines: {node: '>=0.8.19'}

  indent-string@3.2.0:
    resolution: {integrity: sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=, tarball: http://npm.zhenguanyu.com/indent-string/download/indent-string-3.2.0.tgz}
    engines: {node: '>=4'}

  indexes-of@1.0.1:
    resolution: {integrity: sha1-8w9xbI4r00bHtn0985FVZqfAVgc=, tarball: http://npm.zhenguanyu.com/indexes-of/download/indexes-of-1.0.1.tgz}

  infer-owner@1.0.4:
    resolution: {integrity: sha1-xM78qo5RBRwqQLos6KPScpWvlGc=, tarball: http://npm.zhenguanyu.com/infer-owner/download/infer-owner-1.0.4.tgz}

  inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=, tarball: http://npm.zhenguanyu.com/inflight/download/inflight-1.0.6.tgz}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.3:
    resolution: {integrity: sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=, tarball: http://npm.zhenguanyu.com/inherits/download/inherits-2.0.3.tgz}

  inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=, tarball: http://npm.zhenguanyu.com/inherits/download/inherits-2.0.4.tgz}

  inquirer@3.3.0:
    resolution: {integrity: sha1-ndLyrXZdyrH/BEO0kUQqILoifck=, tarball: http://npm.zhenguanyu.com/inquirer/download/inquirer-3.3.0.tgz}

  inquirer@6.5.2:
    resolution: {integrity: sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=, tarball: http://npm.zhenguanyu.com/inquirer/download/inquirer-6.5.2.tgz}
    engines: {node: '>=6.0.0'}

  internal-ip@4.3.0:
    resolution: {integrity: sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=, tarball: http://npm.zhenguanyu.com/internal-ip/download/internal-ip-4.3.0.tgz}
    engines: {node: '>=6'}

  internal-slot@1.0.7:
    resolution: {integrity: sha1-wG3Mo+2HQkmIEAewpVI7FyoZCAI=, tarball: http://npm.zhenguanyu.com/internal-slot/download/internal-slot-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  invariant@2.2.4:
    resolution: {integrity: sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=, tarball: http://npm.zhenguanyu.com/invariant/download/invariant-2.2.4.tgz}

  ip-regex@2.1.0:
    resolution: {integrity: sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=, tarball: http://npm.zhenguanyu.com/ip-regex/download/ip-regex-2.1.0.tgz}
    engines: {node: '>=4'}

  ip@1.1.9:
    resolution: {integrity: sha1-jfvMmadU0H9CUxC4aplUaxFR45Y=, tarball: http://npm.zhenguanyu.com/ip/download/ip-1.1.9.tgz}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=, tarball: http://npm.zhenguanyu.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz}
    engines: {node: '>= 0.10'}

  is-absolute-url@2.1.0:
    resolution: {integrity: sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=, tarball: http://npm.zhenguanyu.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz}
    engines: {node: '>=0.10.0'}

  is-absolute-url@3.0.3:
    resolution: {integrity: sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=, tarball: http://npm.zhenguanyu.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz}
    engines: {node: '>=8'}

  is-accessor-descriptor@1.0.1:
    resolution: {integrity: sha1-MiOxBig1RkS4YmDbKbPmk/XO7dQ=, tarball: http://npm.zhenguanyu.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.1.tgz}
    engines: {node: '>= 0.10'}

  is-arguments@1.1.1:
    resolution: {integrity: sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=, tarball: http://npm.zhenguanyu.com/is-arguments/download/is-arguments-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.4:
    resolution: {integrity: sha1-eh+Ss9Ye3SvGXSTxMFMOqT1/rpg=, tarball: http://npm.zhenguanyu.com/is-array-buffer/download/is-array-buffer-3.0.4.tgz}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=, tarball: http://npm.zhenguanyu.com/is-arrayish/download/is-arrayish-0.2.1.tgz}

  is-arrayish@0.3.2:
    resolution: {integrity: sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=, tarball: http://npm.zhenguanyu.com/is-arrayish/download/is-arrayish-0.3.2.tgz}

  is-bigint@1.0.4:
    resolution: {integrity: sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=, tarball: http://npm.zhenguanyu.com/is-bigint/download/is-bigint-1.0.4.tgz}

  is-binary-path@1.0.1:
    resolution: {integrity: sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=, tarball: http://npm.zhenguanyu.com/is-binary-path/download/is-binary-path-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=, tarball: http://npm.zhenguanyu.com/is-binary-path/download/is-binary-path-2.1.0.tgz}
    engines: {node: '>=8'}

  is-boolean-object@1.1.2:
    resolution: {integrity: sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=, tarball: http://npm.zhenguanyu.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz}
    engines: {node: '>= 0.4'}

  is-buffer@1.1.6:
    resolution: {integrity: sha1-76ouqdqg16suoTqXsritUf776L4=, tarball: http://npm.zhenguanyu.com/is-buffer/download/is-buffer-1.1.6.tgz}

  is-callable@1.2.7:
    resolution: {integrity: sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=, tarball: http://npm.zhenguanyu.com/is-callable/download/is-callable-1.2.7.tgz}
    engines: {node: '>= 0.4'}

  is-color-stop@1.1.0:
    resolution: {integrity: sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=, tarball: http://npm.zhenguanyu.com/is-color-stop/download/is-color-stop-1.1.0.tgz}

  is-core-module@2.15.1:
    resolution: {integrity: sha1-pzY6Jb7pQv76sN4Tv2qjcsgtzDc=, tarball: http://npm.zhenguanyu.com/is-core-module/download/is-core-module-2.15.1.tgz}
    engines: {node: '>= 0.4'}

  is-data-descriptor@1.0.1:
    resolution: {integrity: sha1-IQkWRCYWbTLqOMQFweCUXZ5qTus=, tarball: http://npm.zhenguanyu.com/is-data-descriptor/download/is-data-descriptor-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.1:
    resolution: {integrity: sha1-S006URtw89wm1CwDypylFdhHdZ8=, tarball: http://npm.zhenguanyu.com/is-data-view/download/is-data-view-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  is-date-object@1.0.5:
    resolution: {integrity: sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=, tarball: http://npm.zhenguanyu.com/is-date-object/download/is-date-object-1.0.5.tgz}
    engines: {node: '>= 0.4'}

  is-descriptor@0.1.7:
    resolution: {integrity: sha1-JyfrYf14nc1b3w7UVp9VHS/jvjM=, tarball: http://npm.zhenguanyu.com/is-descriptor/download/is-descriptor-0.1.7.tgz}
    engines: {node: '>= 0.4'}

  is-descriptor@1.0.3:
    resolution: {integrity: sha1-ktJ8s80xHEl3pNtH30VyNKE8swY=, tarball: http://npm.zhenguanyu.com/is-descriptor/download/is-descriptor-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-directory@0.3.1:
    resolution: {integrity: sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=, tarball: http://npm.zhenguanyu.com/is-directory/download/is-directory-0.3.1.tgz}
    engines: {node: '>=0.10.0'}

  is-docker@2.2.1:
    resolution: {integrity: sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=, tarball: http://npm.zhenguanyu.com/is-docker/download/is-docker-2.2.1.tgz}
    engines: {node: '>=8'}
    hasBin: true

  is-extendable@0.1.1:
    resolution: {integrity: sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=, tarball: http://npm.zhenguanyu.com/is-extendable/download/is-extendable-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  is-extendable@1.0.1:
    resolution: {integrity: sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=, tarball: http://npm.zhenguanyu.com/is-extendable/download/is-extendable-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=, tarball: http://npm.zhenguanyu.com/is-extglob/download/is-extglob-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@1.0.0:
    resolution: {integrity: sha1-754xOG8DGn8NZDr4L95QxFfvAMs=, tarball: http://npm.zhenguanyu.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@2.0.0:
    resolution: {integrity: sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=, tarball: http://npm.zhenguanyu.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz}
    engines: {node: '>=4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=, tarball: http://npm.zhenguanyu.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz}
    engines: {node: '>=8'}

  is-glob@3.1.0:
    resolution: {integrity: sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=, tarball: http://npm.zhenguanyu.com/is-glob/download/is-glob-3.1.0.tgz}
    engines: {node: '>=0.10.0'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=, tarball: http://npm.zhenguanyu.com/is-glob/download/is-glob-4.0.3.tgz}
    engines: {node: '>=0.10.0'}

  is-negative-zero@2.0.3:
    resolution: {integrity: sha1-ztkDoCespjgbd3pXQwadc3akl0c=, tarball: http://npm.zhenguanyu.com/is-negative-zero/download/is-negative-zero-2.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-number-object@1.0.7:
    resolution: {integrity: sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=, tarball: http://npm.zhenguanyu.com/is-number-object/download/is-number-object-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  is-number@3.0.0:
    resolution: {integrity: sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=, tarball: http://npm.zhenguanyu.com/is-number/download/is-number-3.0.0.tgz}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=, tarball: http://npm.zhenguanyu.com/is-number/download/is-number-7.0.0.tgz}
    engines: {node: '>=0.12.0'}

  is-obj@1.0.1:
    resolution: {integrity: sha1-PkcprB9f3gJc19g6iW2rn09n2w8=, tarball: http://npm.zhenguanyu.com/is-obj/download/is-obj-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  is-obj@2.0.0:
    resolution: {integrity: sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=, tarball: http://npm.zhenguanyu.com/is-obj/download/is-obj-2.0.0.tgz}
    engines: {node: '>=8'}

  is-observable@1.1.0:
    resolution: {integrity: sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=, tarball: http://npm.zhenguanyu.com/is-observable/download/is-observable-1.1.0.tgz}
    engines: {node: '>=4'}

  is-path-cwd@1.0.0:
    resolution: {integrity: sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=, tarball: http://npm.zhenguanyu.com/is-path-cwd/download/is-path-cwd-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  is-path-cwd@2.2.0:
    resolution: {integrity: sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=, tarball: http://npm.zhenguanyu.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz}
    engines: {node: '>=6'}

  is-path-in-cwd@1.0.1:
    resolution: {integrity: sha1-WsSLNF72dTOb1sekipEhELJBz1I=, tarball: http://npm.zhenguanyu.com/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  is-path-in-cwd@2.1.0:
    resolution: {integrity: sha1-v+Lcomxp85cmWkAJljYCk1oFOss=, tarball: http://npm.zhenguanyu.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz}
    engines: {node: '>=6'}

  is-path-inside@1.0.1:
    resolution: {integrity: sha1-jvW33lBDej/cprToZe96pVy0gDY=, tarball: http://npm.zhenguanyu.com/is-path-inside/download/is-path-inside-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  is-path-inside@2.1.0:
    resolution: {integrity: sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=, tarball: http://npm.zhenguanyu.com/is-path-inside/download/is-path-inside-2.1.0.tgz}
    engines: {node: '>=6'}

  is-plain-obj@1.1.0:
    resolution: {integrity: sha1-caUMhCnfync8kqOQpKA7OfzVHT4=, tarball: http://npm.zhenguanyu.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz}
    engines: {node: '>=0.10.0'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=, tarball: http://npm.zhenguanyu.com/is-plain-object/download/is-plain-object-2.0.4.tgz}
    engines: {node: '>=0.10.0'}

  is-promise@2.2.2:
    resolution: {integrity: sha1-OauVnMv5p3TPB597QMeib3YxNfE=, tarball: http://npm.zhenguanyu.com/is-promise/download/is-promise-2.2.2.tgz}

  is-regex@1.1.4:
    resolution: {integrity: sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=, tarball: http://npm.zhenguanyu.com/is-regex/download/is-regex-1.1.4.tgz}
    engines: {node: '>= 0.4'}

  is-regexp@1.0.0:
    resolution: {integrity: sha1-/S2INUXEa6xaYz57mgnof6LLUGk=, tarball: http://npm.zhenguanyu.com/is-regexp/download/is-regexp-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  is-resolvable@1.1.0:
    resolution: {integrity: sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=, tarball: http://npm.zhenguanyu.com/is-resolvable/download/is-resolvable-1.1.0.tgz}

  is-shared-array-buffer@1.0.3:
    resolution: {integrity: sha1-Ejfxy6BZzbYkMdN43MN9loAYFog=, tarball: http://npm.zhenguanyu.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-stream@1.1.0:
    resolution: {integrity: sha1-EtSj3U5o4Lec6428hBc66A2RykQ=, tarball: http://npm.zhenguanyu.com/is-stream/download/is-stream-1.1.0.tgz}
    engines: {node: '>=0.10.0'}

  is-stream@2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=, tarball: http://npm.zhenguanyu.com/is-stream/download/is-stream-2.0.1.tgz}
    engines: {node: '>=8'}

  is-string@1.0.7:
    resolution: {integrity: sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=, tarball: http://npm.zhenguanyu.com/is-string/download/is-string-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  is-symbol@1.0.4:
    resolution: {integrity: sha1-ptrJO2NbBjymhyI23oiRClevE5w=, tarball: http://npm.zhenguanyu.com/is-symbol/download/is-symbol-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.13:
    resolution: {integrity: sha1-1sXKVt9iM0lZMi19fdHMpQ3r4ik=, tarball: http://npm.zhenguanyu.com/is-typed-array/download/is-typed-array-1.1.13.tgz}
    engines: {node: '>= 0.4'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=, tarball: http://npm.zhenguanyu.com/is-typedarray/download/is-typedarray-1.0.0.tgz}

  is-weakref@1.0.2:
    resolution: {integrity: sha1-lSnzg6kzggXol2XgOS78LxAPBvI=, tarball: http://npm.zhenguanyu.com/is-weakref/download/is-weakref-1.0.2.tgz}

  is-what@3.14.1:
    resolution: {integrity: sha1-4SIvRt3ahd6tD9HJ3xMXYOd3VcE=, tarball: http://npm.zhenguanyu.com/is-what/download/is-what-3.14.1.tgz}

  is-windows@1.0.2:
    resolution: {integrity: sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=, tarball: http://npm.zhenguanyu.com/is-windows/download/is-windows-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  is-wsl@1.1.0:
    resolution: {integrity: sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=, tarball: http://npm.zhenguanyu.com/is-wsl/download/is-wsl-1.1.0.tgz}
    engines: {node: '>=4'}

  is-wsl@2.2.0:
    resolution: {integrity: sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=, tarball: http://npm.zhenguanyu.com/is-wsl/download/is-wsl-2.2.0.tgz}
    engines: {node: '>=8'}

  isarray@1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=, tarball: http://npm.zhenguanyu.com/isarray/download/isarray-1.0.0.tgz}

  isarray@2.0.5:
    resolution: {integrity: sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=, tarball: http://npm.zhenguanyu.com/isarray/download/isarray-2.0.5.tgz}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=, tarball: http://npm.zhenguanyu.com/isexe/download/isexe-2.0.0.tgz}

  isobject@2.1.0:
    resolution: {integrity: sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=, tarball: http://npm.zhenguanyu.com/isobject/download/isobject-2.1.0.tgz}
    engines: {node: '>=0.10.0'}

  isobject@3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=, tarball: http://npm.zhenguanyu.com/isobject/download/isobject-3.0.1.tgz}
    engines: {node: '>=0.10.0'}

  isstream@0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=, tarball: http://npm.zhenguanyu.com/isstream/download/isstream-0.1.2.tgz}

  javascript-stringify@1.6.0:
    resolution: {integrity: sha1-FC0RHzpuPa6PSpr9d9RYVbWpzOM=, tarball: http://npm.zhenguanyu.com/javascript-stringify/download/javascript-stringify-1.6.0.tgz}

  js-levenshtein@1.1.6:
    resolution: {integrity: sha1-xs7ljrNVA3LfjeuF+tXOZs4B1Z0=, tarball: http://npm.zhenguanyu.com/js-levenshtein/download/js-levenshtein-1.1.6.tgz}
    engines: {node: '>=0.10.0'}

  js-message@1.0.7:
    resolution: {integrity: sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc=, tarball: http://npm.zhenguanyu.com/js-message/download/js-message-1.0.7.tgz}
    engines: {node: '>=0.6.0'}

  js-queue@2.0.2:
    resolution: {integrity: sha1-C+WQM4+QOzbHPTPDGIOoIUEs1II=, tarball: http://npm.zhenguanyu.com/js-queue/download/js-queue-2.0.2.tgz}
    engines: {node: '>=1.0.0'}

  js-tokens@3.0.2:
    resolution: {integrity: sha1-mGbfOVECEw449/mWvOtlRDIJwls=, tarball: http://npm.zhenguanyu.com/js-tokens/download/js-tokens-3.0.2.tgz}

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=, tarball: http://npm.zhenguanyu.com/js-tokens/download/js-tokens-4.0.0.tgz}

  js-yaml@3.14.1:
    resolution: {integrity: sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=, tarball: http://npm.zhenguanyu.com/js-yaml/download/js-yaml-3.14.1.tgz}
    hasBin: true

  jsbn@0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=, tarball: http://npm.zhenguanyu.com/jsbn/download/jsbn-0.1.1.tgz}

  jsdoctypeparser@3.1.0:
    resolution: {integrity: sha1-L2X3UWXE2cYyu0/aE+02t4MhpDs=, tarball: http://npm.zhenguanyu.com/jsdoctypeparser/download/jsdoctypeparser-3.1.0.tgz}
    engines: {node: '>=6'}

  jsesc@3.0.2:
    resolution: {integrity: sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=, tarball: http://npm.zhenguanyu.com/jsesc/download/jsesc-3.0.2.tgz}
    engines: {node: '>=6'}
    hasBin: true

  json-parse-better-errors@1.0.2:
    resolution: {integrity: sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=, tarball: http://npm.zhenguanyu.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=, tarball: http://npm.zhenguanyu.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz}

  json-schema-traverse@0.3.1:
    resolution: {integrity: sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A=, tarball: http://npm.zhenguanyu.com/json-schema-traverse/download/json-schema-traverse-0.3.1.tgz}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=, tarball: http://npm.zhenguanyu.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz}

  json-schema@0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=, tarball: http://npm.zhenguanyu.com/json-schema/download/json-schema-0.4.0.tgz}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=, tarball: http://npm.zhenguanyu.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz}

  json-stringify-safe@5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=, tarball: http://npm.zhenguanyu.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz}

  json5@0.5.1:
    resolution: {integrity: sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=, tarball: http://npm.zhenguanyu.com/json5/download/json5-0.5.1.tgz}
    hasBin: true

  json5@1.0.2:
    resolution: {integrity: sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=, tarball: http://npm.zhenguanyu.com/json5/download/json5-1.0.2.tgz}
    hasBin: true

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=, tarball: http://npm.zhenguanyu.com/json5/download/json5-2.2.3.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@4.0.0:
    resolution: {integrity: sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=, tarball: http://npm.zhenguanyu.com/jsonfile/download/jsonfile-4.0.0.tgz}

  jsprim@1.4.2:
    resolution: {integrity: sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=, tarball: http://npm.zhenguanyu.com/jsprim/download/jsprim-1.4.2.tgz}
    engines: {node: '>=0.6.0'}

  killable@1.0.1:
    resolution: {integrity: sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=, tarball: http://npm.zhenguanyu.com/killable/download/killable-1.0.1.tgz}

  kind-of@3.2.2:
    resolution: {integrity: sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=, tarball: http://npm.zhenguanyu.com/kind-of/download/kind-of-3.2.2.tgz}
    engines: {node: '>=0.10.0'}

  kind-of@4.0.0:
    resolution: {integrity: sha1-IIE989cSkosgc3hpGkUGb65y3Vc=, tarball: http://npm.zhenguanyu.com/kind-of/download/kind-of-4.0.0.tgz}
    engines: {node: '>=0.10.0'}

  kind-of@6.0.3:
    resolution: {integrity: sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=, tarball: http://npm.zhenguanyu.com/kind-of/download/kind-of-6.0.3.tgz}
    engines: {node: '>=0.10.0'}

  launch-editor-middleware@2.9.1:
    resolution: {integrity: sha1-0SVjOdOiHKAANbpdf5vFOsGS9Gw=, tarball: http://npm.zhenguanyu.com/launch-editor-middleware/download/launch-editor-middleware-2.9.1.tgz}

  launch-editor@2.9.1:
    resolution: {integrity: sha1-JT8XO9RB40LUNEtNrlgpGrtCUEc=, tarball: http://npm.zhenguanyu.com/launch-editor/download/launch-editor-2.9.1.tgz}

  less-loader@4.1.0:
    resolution: {integrity: sha1-LBNSxbCaT4QQFJAnT9UWdN5BNj4=, tarball: http://npm.zhenguanyu.com/less-loader/download/less-loader-4.1.0.tgz}
    engines: {node: '>= 4.8 < 5.0.0 || >= 5.10'}
    peerDependencies:
      less: ^2.3.1 || ^3.0.0
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0

  less@3.13.1:
    resolution: {integrity: sha1-DryR0qDpwMZzW4PUlrCrBYMHeQk=, tarball: http://npm.zhenguanyu.com/less/download/less-3.13.1.tgz}
    engines: {node: '>=6'}
    hasBin: true

  levn@0.3.0:
    resolution: {integrity: sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=, tarball: http://npm.zhenguanyu.com/levn/download/levn-0.3.0.tgz}
    engines: {node: '>= 0.8.0'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=, tarball: http://npm.zhenguanyu.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz}

  lint-staged@8.2.1:
    resolution: {integrity: sha1-dS/PIi2dKPMjo7gPHmaPNlT/Ih8=, tarball: http://npm.zhenguanyu.com/lint-staged/download/lint-staged-8.2.1.tgz}
    hasBin: true

  listr-silent-renderer@1.1.1:
    resolution: {integrity: sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=, tarball: http://npm.zhenguanyu.com/listr-silent-renderer/download/listr-silent-renderer-1.1.1.tgz}
    engines: {node: '>=4'}

  listr-update-renderer@0.5.0:
    resolution: {integrity: sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=, tarball: http://npm.zhenguanyu.com/listr-update-renderer/download/listr-update-renderer-0.5.0.tgz}
    engines: {node: '>=6'}
    peerDependencies:
      listr: ^0.14.2

  listr-verbose-renderer@0.5.0:
    resolution: {integrity: sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=, tarball: http://npm.zhenguanyu.com/listr-verbose-renderer/download/listr-verbose-renderer-0.5.0.tgz}
    engines: {node: '>=4'}

  listr@0.14.3:
    resolution: {integrity: sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=, tarball: http://npm.zhenguanyu.com/listr/download/listr-0.14.3.tgz}
    engines: {node: '>=6'}

  loader-fs-cache@1.0.3:
    resolution: {integrity: sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=, tarball: http://npm.zhenguanyu.com/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz}

  loader-runner@2.4.0:
    resolution: {integrity: sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=, tarball: http://npm.zhenguanyu.com/loader-runner/download/loader-runner-2.4.0.tgz}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}

  loader-utils@0.2.17:
    resolution: {integrity: sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=, tarball: http://npm.zhenguanyu.com/loader-utils/download/loader-utils-0.2.17.tgz}

  loader-utils@1.4.2:
    resolution: {integrity: sha1-KalX86Y5c4g+toTxD/09FR/sAaM=, tarball: http://npm.zhenguanyu.com/loader-utils/download/loader-utils-1.4.2.tgz}
    engines: {node: '>=4.0.0'}

  loader-utils@2.0.4:
    resolution: {integrity: sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=, tarball: http://npm.zhenguanyu.com/loader-utils/download/loader-utils-2.0.4.tgz}
    engines: {node: '>=8.9.0'}

  locate-path@2.0.0:
    resolution: {integrity: sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=, tarball: http://npm.zhenguanyu.com/locate-path/download/locate-path-2.0.0.tgz}
    engines: {node: '>=4'}

  locate-path@3.0.0:
    resolution: {integrity: sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=, tarball: http://npm.zhenguanyu.com/locate-path/download/locate-path-3.0.0.tgz}
    engines: {node: '>=6'}

  locate-path@5.0.0:
    resolution: {integrity: sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=, tarball: http://npm.zhenguanyu.com/locate-path/download/locate-path-5.0.0.tgz}
    engines: {node: '>=8'}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=, tarball: http://npm.zhenguanyu.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz}

  lodash.defaultsdeep@4.6.1:
    resolution: {integrity: sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=, tarball: http://npm.zhenguanyu.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz}

  lodash.kebabcase@4.1.1:
    resolution: {integrity: sha1-hImxyw0p/4gZXM7KRI/21swpXDY=, tarball: http://npm.zhenguanyu.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz}

  lodash.mapvalues@4.6.0:
    resolution: {integrity: sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=, tarball: http://npm.zhenguanyu.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz}

  lodash.memoize@4.1.2:
    resolution: {integrity: sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=, tarball: http://npm.zhenguanyu.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz}

  lodash.transform@4.6.0:
    resolution: {integrity: sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=, tarball: http://npm.zhenguanyu.com/lodash.transform/download/lodash.transform-4.6.0.tgz}

  lodash.uniq@4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=, tarball: http://npm.zhenguanyu.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, tarball: http://npm.zhenguanyu.com/lodash/download/lodash-4.17.21.tgz}

  log-symbols@1.0.2:
    resolution: {integrity: sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=, tarball: http://npm.zhenguanyu.com/log-symbols/download/log-symbols-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  log-symbols@2.2.0:
    resolution: {integrity: sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=, tarball: http://npm.zhenguanyu.com/log-symbols/download/log-symbols-2.2.0.tgz}
    engines: {node: '>=4'}

  log-update@2.3.0:
    resolution: {integrity: sha1-iDKP19HOeTiykoN0bwsbwSayRwg=, tarball: http://npm.zhenguanyu.com/log-update/download/log-update-2.3.0.tgz}
    engines: {node: '>=4'}

  loglevel@1.9.2:
    resolution: {integrity: sha1-wuAo1sdXcgEH305kUIUw22Yhugg=, tarball: http://npm.zhenguanyu.com/loglevel/download/loglevel-1.9.2.tgz}
    engines: {node: '>= 0.6.0'}

  loose-envify@1.4.0:
    resolution: {integrity: sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=, tarball: http://npm.zhenguanyu.com/loose-envify/download/loose-envify-1.4.0.tgz}
    hasBin: true

  lower-case@1.1.4:
    resolution: {integrity: sha1-miyr0bno4K6ZOkv31YdcOcQujqw=, tarball: http://npm.zhenguanyu.com/lower-case/download/lower-case-1.1.4.tgz}

  lru-cache@4.1.5:
    resolution: {integrity: sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=, tarball: http://npm.zhenguanyu.com/lru-cache/download/lru-cache-4.1.5.tgz}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=, tarball: http://npm.zhenguanyu.com/lru-cache/download/lru-cache-5.1.1.tgz}

  make-dir@1.3.0:
    resolution: {integrity: sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=, tarball: http://npm.zhenguanyu.com/make-dir/download/make-dir-1.3.0.tgz}
    engines: {node: '>=4'}

  make-dir@2.1.0:
    resolution: {integrity: sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=, tarball: http://npm.zhenguanyu.com/make-dir/download/make-dir-2.1.0.tgz}
    engines: {node: '>=6'}

  make-dir@3.1.0:
    resolution: {integrity: sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=, tarball: http://npm.zhenguanyu.com/make-dir/download/make-dir-3.1.0.tgz}
    engines: {node: '>=8'}

  map-cache@0.2.2:
    resolution: {integrity: sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=, tarball: http://npm.zhenguanyu.com/map-cache/download/map-cache-0.2.2.tgz}
    engines: {node: '>=0.10.0'}

  map-visit@1.0.0:
    resolution: {integrity: sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=, tarball: http://npm.zhenguanyu.com/map-visit/download/map-visit-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  matcher@1.1.1:
    resolution: {integrity: sha1-UdgwHhOPhAmCszixFrsMCa9iwcI=, tarball: http://npm.zhenguanyu.com/matcher/download/matcher-1.1.1.tgz}
    engines: {node: '>=4'}

  md5.js@1.3.5:
    resolution: {integrity: sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=, tarball: http://npm.zhenguanyu.com/md5.js/download/md5.js-1.3.5.tgz}

  mdn-data@2.0.14:
    resolution: {integrity: sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=, tarball: http://npm.zhenguanyu.com/mdn-data/download/mdn-data-2.0.14.tgz}

  mdn-data@2.0.4:
    resolution: {integrity: sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=, tarball: http://npm.zhenguanyu.com/mdn-data/download/mdn-data-2.0.4.tgz}

  media-typer@0.3.0:
    resolution: {integrity: sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=, tarball: http://npm.zhenguanyu.com/media-typer/download/media-typer-0.3.0.tgz}
    engines: {node: '>= 0.6'}

  memory-fs@0.4.1:
    resolution: {integrity: sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=, tarball: http://npm.zhenguanyu.com/memory-fs/download/memory-fs-0.4.1.tgz}

  memory-fs@0.5.0:
    resolution: {integrity: sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=, tarball: http://npm.zhenguanyu.com/memory-fs/download/memory-fs-0.5.0.tgz}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=, tarball: http://npm.zhenguanyu.com/merge-descriptors/download/merge-descriptors-1.0.3.tgz}

  merge-source-map@1.1.0:
    resolution: {integrity: sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=, tarball: http://npm.zhenguanyu.com/merge-source-map/download/merge-source-map-1.1.0.tgz}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=, tarball: http://npm.zhenguanyu.com/merge-stream/download/merge-stream-2.0.0.tgz}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=, tarball: http://npm.zhenguanyu.com/merge2/download/merge2-1.4.1.tgz}
    engines: {node: '>= 8'}

  methods@1.1.2:
    resolution: {integrity: sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=, tarball: http://npm.zhenguanyu.com/methods/download/methods-1.1.2.tgz}
    engines: {node: '>= 0.6'}

  micromatch@3.1.10:
    resolution: {integrity: sha1-cIWbyVyYQJUvNZoGij/En57PrCM=, tarball: http://npm.zhenguanyu.com/micromatch/download/micromatch-3.1.10.tgz}
    engines: {node: '>=0.10.0'}

  miller-rabin@4.0.1:
    resolution: {integrity: sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=, tarball: http://npm.zhenguanyu.com/miller-rabin/download/miller-rabin-4.0.1.tgz}
    hasBin: true

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=, tarball: http://npm.zhenguanyu.com/mime-db/download/mime-db-1.52.0.tgz}
    engines: {node: '>= 0.6'}

  mime-db@1.53.0:
    resolution: {integrity: sha1-PLY82CD8KYltnU6MMqtPzXTMtEc=, tarball: http://npm.zhenguanyu.com/mime-db/download/mime-db-1.53.0.tgz}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=, tarball: http://npm.zhenguanyu.com/mime-types/download/mime-types-2.1.35.tgz}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=, tarball: http://npm.zhenguanyu.com/mime/download/mime-1.6.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  mime@2.6.0:
    resolution: {integrity: sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=, tarball: http://npm.zhenguanyu.com/mime/download/mime-2.6.0.tgz}
    engines: {node: '>=4.0.0'}
    hasBin: true

  mimic-fn@1.2.0:
    resolution: {integrity: sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=, tarball: http://npm.zhenguanyu.com/mimic-fn/download/mimic-fn-1.2.0.tgz}
    engines: {node: '>=4'}

  mimic-fn@2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=, tarball: http://npm.zhenguanyu.com/mimic-fn/download/mimic-fn-2.1.0.tgz}
    engines: {node: '>=6'}

  mini-css-extract-plugin@0.8.2:
    resolution: {integrity: sha1-qHXhab6yfIivd92WJ3HJ7tw9oWE=, tarball: http://npm.zhenguanyu.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.8.2.tgz}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.4.0

  minimalistic-assert@1.0.1:
    resolution: {integrity: sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=, tarball: http://npm.zhenguanyu.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz}

  minimalistic-crypto-utils@1.0.1:
    resolution: {integrity: sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=, tarball: http://npm.zhenguanyu.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=, tarball: http://npm.zhenguanyu.com/minimatch/download/minimatch-3.1.2.tgz}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=, tarball: http://npm.zhenguanyu.com/minimist/download/minimist-1.2.8.tgz}

  mississippi@2.0.0:
    resolution: {integrity: sha1-NEKlCPr8KFAEhv7qmUCWduTuWm8=, tarball: http://npm.zhenguanyu.com/mississippi/download/mississippi-2.0.0.tgz}
    engines: {node: '>=4.0.0'}

  mississippi@3.0.0:
    resolution: {integrity: sha1-6goykfl+C16HdrNj1fChLZTGcCI=, tarball: http://npm.zhenguanyu.com/mississippi/download/mississippi-3.0.0.tgz}
    engines: {node: '>=4.0.0'}

  mixin-deep@1.3.2:
    resolution: {integrity: sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=, tarball: http://npm.zhenguanyu.com/mixin-deep/download/mixin-deep-1.3.2.tgz}
    engines: {node: '>=0.10.0'}

  mkdirp@0.5.6:
    resolution: {integrity: sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=, tarball: http://npm.zhenguanyu.com/mkdirp/download/mkdirp-0.5.6.tgz}
    hasBin: true

  move-concurrently@1.0.1:
    resolution: {integrity: sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=, tarball: http://npm.zhenguanyu.com/move-concurrently/download/move-concurrently-1.0.1.tgz}
    deprecated: This package is no longer supported.

  ms@2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=, tarball: http://npm.zhenguanyu.com/ms/download/ms-2.0.0.tgz}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=, tarball: http://npm.zhenguanyu.com/ms/download/ms-2.1.3.tgz}

  multicast-dns-service-types@1.1.0:
    resolution: {integrity: sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=, tarball: http://npm.zhenguanyu.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz}

  multicast-dns@6.2.3:
    resolution: {integrity: sha1-oOx72QVcQoL3kMPIL04o2zsxsik=, tarball: http://npm.zhenguanyu.com/multicast-dns/download/multicast-dns-6.2.3.tgz}
    hasBin: true

  mute-stream@0.0.7:
    resolution: {integrity: sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=, tarball: http://npm.zhenguanyu.com/mute-stream/download/mute-stream-0.0.7.tgz}

  mz@2.7.0:
    resolution: {integrity: sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=, tarball: http://npm.zhenguanyu.com/mz/download/mz-2.7.0.tgz}

  nan@2.22.0:
    resolution: {integrity: sha1-MbxDP8MyE8l7rTZAS7aAY95gTeM=, tarball: http://npm.zhenguanyu.com/nan/download/nan-2.22.0.tgz}

  nanoid@3.3.7:
    resolution: {integrity: sha1-0MMBppG8jVTvoKIibM8/4v1la9g=, tarball: http://npm.zhenguanyu.com/nanoid/download/nanoid-3.3.7.tgz}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanomatch@1.2.13:
    resolution: {integrity: sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=, tarball: http://npm.zhenguanyu.com/nanomatch/download/nanomatch-1.2.13.tgz}
    engines: {node: '>=0.10.0'}

  native-request@1.1.2:
    resolution: {integrity: sha1-tneVJ1dCnbbNQZcqKcO3gZd0E+0=, tarball: http://npm.zhenguanyu.com/native-request/download/native-request-1.1.2.tgz}

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=, tarball: http://npm.zhenguanyu.com/natural-compare/download/natural-compare-1.4.0.tgz}

  negotiator@0.6.3:
    resolution: {integrity: sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=, tarball: http://npm.zhenguanyu.com/negotiator/download/negotiator-0.6.3.tgz}
    engines: {node: '>= 0.6'}

  negotiator@0.6.4:
    resolution: {integrity: sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=, tarball: http://npm.zhenguanyu.com/negotiator/download/negotiator-0.6.4.tgz}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha1-tKr7k+OustgXTKU88WOrfXMIMF8=, tarball: http://npm.zhenguanyu.com/neo-async/download/neo-async-2.6.2.tgz}

  nice-try@1.0.5:
    resolution: {integrity: sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=, tarball: http://npm.zhenguanyu.com/nice-try/download/nice-try-1.0.5.tgz}

  no-case@2.3.2:
    resolution: {integrity: sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=, tarball: http://npm.zhenguanyu.com/no-case/download/no-case-2.3.2.tgz}

  node-forge@0.10.0:
    resolution: {integrity: sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=, tarball: http://npm.zhenguanyu.com/node-forge/download/node-forge-0.10.0.tgz}
    engines: {node: '>= 6.0.0'}

  node-ipc@9.2.1:
    resolution: {integrity: sha1-sy9mEV+dbOhB3E7CAJ1qcz+Yu2s=, tarball: http://npm.zhenguanyu.com/node-ipc/download/node-ipc-9.2.1.tgz}
    engines: {node: '>=8.0.0'}

  node-libs-browser@2.2.1:
    resolution: {integrity: sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=, tarball: http://npm.zhenguanyu.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz}

  node-releases@2.0.18:
    resolution: {integrity: sha1-8BDo014v6NaylE8D9wIT7O3Eyj8=, tarball: http://npm.zhenguanyu.com/node-releases/download/node-releases-2.0.18.tgz}

  normalize-package-data@2.5.0:
    resolution: {integrity: sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=, tarball: http://npm.zhenguanyu.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz}

  normalize-path@2.1.1:
    resolution: {integrity: sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=, tarball: http://npm.zhenguanyu.com/normalize-path/download/normalize-path-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  normalize-path@3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=, tarball: http://npm.zhenguanyu.com/normalize-path/download/normalize-path-3.0.0.tgz}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=, tarball: http://npm.zhenguanyu.com/normalize-range/download/normalize-range-0.1.2.tgz}
    engines: {node: '>=0.10.0'}

  normalize-url@1.9.1:
    resolution: {integrity: sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=, tarball: http://npm.zhenguanyu.com/normalize-url/download/normalize-url-1.9.1.tgz}
    engines: {node: '>=4'}

  normalize-url@3.3.0:
    resolution: {integrity: sha1-suHE3E98bVd0PfczpPWXjRhlBVk=, tarball: http://npm.zhenguanyu.com/normalize-url/download/normalize-url-3.3.0.tgz}
    engines: {node: '>=6'}

  npm-path@2.0.4:
    resolution: {integrity: sha1-xkE0el/51qCeTZvOVYDE9QUnjmQ=, tarball: http://npm.zhenguanyu.com/npm-path/download/npm-path-2.0.4.tgz}
    engines: {node: '>=0.8'}
    hasBin: true

  npm-run-path@2.0.2:
    resolution: {integrity: sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=, tarball: http://npm.zhenguanyu.com/npm-run-path/download/npm-run-path-2.0.2.tgz}
    engines: {node: '>=4'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha1-t+zR5e1T2o43pV4cImnguX7XSOo=, tarball: http://npm.zhenguanyu.com/npm-run-path/download/npm-run-path-4.0.1.tgz}
    engines: {node: '>=8'}

  npm-which@3.0.1:
    resolution: {integrity: sha1-kiXybsOihcIJyuZ8OxGmtKtxQKo=, tarball: http://npm.zhenguanyu.com/npm-which/download/npm-which-3.0.1.tgz}
    engines: {node: '>=4.2.0'}
    hasBin: true

  nth-check@1.0.2:
    resolution: {integrity: sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=, tarball: http://npm.zhenguanyu.com/nth-check/download/nth-check-1.0.2.tgz}

  nth-check@2.1.1:
    resolution: {integrity: sha1-yeq0KO/842zWuSySS9sADvHx7R0=, tarball: http://npm.zhenguanyu.com/nth-check/download/nth-check-2.1.1.tgz}

  num2fraction@1.2.2:
    resolution: {integrity: sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=, tarball: http://npm.zhenguanyu.com/num2fraction/download/num2fraction-1.2.2.tgz}

  number-is-nan@1.0.1:
    resolution: {integrity: sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=, tarball: http://npm.zhenguanyu.com/number-is-nan/download/number-is-nan-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  oauth-sign@0.9.0:
    resolution: {integrity: sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=, tarball: http://npm.zhenguanyu.com/oauth-sign/download/oauth-sign-0.9.0.tgz}

  object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=, tarball: http://npm.zhenguanyu.com/object-assign/download/object-assign-4.1.1.tgz}
    engines: {node: '>=0.10.0'}

  object-copy@0.1.0:
    resolution: {integrity: sha1-fn2Fi3gb18mRpBupde04EnVOmYw=, tarball: http://npm.zhenguanyu.com/object-copy/download/object-copy-0.1.0.tgz}
    engines: {node: '>=0.10.0'}

  object-hash@1.3.1:
    resolution: {integrity: sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=, tarball: http://npm.zhenguanyu.com/object-hash/download/object-hash-1.3.1.tgz}
    engines: {node: '>= 0.10.0'}

  object-inspect@1.13.2:
    resolution: {integrity: sha1-3qAIhGf7mR5nr0BYFHokgkowQ/8=, tarball: http://npm.zhenguanyu.com/object-inspect/download/object-inspect-1.13.2.tgz}
    engines: {node: '>= 0.4'}

  object-is@1.1.6:
    resolution: {integrity: sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=, tarball: http://npm.zhenguanyu.com/object-is/download/object-is-1.1.6.tgz}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha1-HEfyct8nfzsdrwYWd9nILiMixg4=, tarball: http://npm.zhenguanyu.com/object-keys/download/object-keys-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  object-visit@1.0.1:
    resolution: {integrity: sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=, tarball: http://npm.zhenguanyu.com/object-visit/download/object-visit-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  object.assign@4.1.5:
    resolution: {integrity: sha1-OoM/mrf9uA/J6NIwDIA9IW2P27A=, tarball: http://npm.zhenguanyu.com/object.assign/download/object.assign-4.1.5.tgz}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha1-9xldipuXvZXLwZmeqTns0aKwDGU=, tarball: http://npm.zhenguanyu.com/object.fromentries/download/object.fromentries-2.0.8.tgz}
    engines: {node: '>= 0.4'}

  object.getownpropertydescriptors@2.1.8:
    resolution: {integrity: sha1-Lx/gYG7Bp2WBVMzU9yhQT2lmeSM=, tarball: http://npm.zhenguanyu.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.8.tgz}
    engines: {node: '>= 0.8'}

  object.groupby@1.0.3:
    resolution: {integrity: sha1-mxJcNiOBKfb3thlUoecXYUjVAC4=, tarball: http://npm.zhenguanyu.com/object.groupby/download/object.groupby-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  object.pick@1.3.0:
    resolution: {integrity: sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=, tarball: http://npm.zhenguanyu.com/object.pick/download/object.pick-1.3.0.tgz}
    engines: {node: '>=0.10.0'}

  object.values@1.2.0:
    resolution: {integrity: sha1-ZUBanZLO5orC0wMALguEcKTZqxs=, tarball: http://npm.zhenguanyu.com/object.values/download/object.values-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  obuf@1.1.2:
    resolution: {integrity: sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=, tarball: http://npm.zhenguanyu.com/obuf/download/obuf-1.1.2.tgz}

  on-finished@2.4.1:
    resolution: {integrity: sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=, tarball: http://npm.zhenguanyu.com/on-finished/download/on-finished-2.4.1.tgz}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=, tarball: http://npm.zhenguanyu.com/on-headers/download/on-headers-1.0.2.tgz}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=, tarball: http://npm.zhenguanyu.com/once/download/once-1.4.0.tgz}

  onetime@2.0.1:
    resolution: {integrity: sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=, tarball: http://npm.zhenguanyu.com/onetime/download/onetime-2.0.1.tgz}
    engines: {node: '>=4'}

  onetime@5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=, tarball: http://npm.zhenguanyu.com/onetime/download/onetime-5.1.2.tgz}
    engines: {node: '>=6'}

  open@6.4.0:
    resolution: {integrity: sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=, tarball: http://npm.zhenguanyu.com/open/download/open-6.4.0.tgz}
    engines: {node: '>=8'}

  opener@1.5.2:
    resolution: {integrity: sha1-XTfh81B3udysQwE3InGv3rKhNZg=, tarball: http://npm.zhenguanyu.com/opener/download/opener-1.5.2.tgz}
    hasBin: true

  opn@5.5.0:
    resolution: {integrity: sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=, tarball: http://npm.zhenguanyu.com/opn/download/opn-5.5.0.tgz}
    engines: {node: '>=4'}

  optionator@0.8.3:
    resolution: {integrity: sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=, tarball: http://npm.zhenguanyu.com/optionator/download/optionator-0.8.3.tgz}
    engines: {node: '>= 0.8.0'}

  ora@3.4.0:
    resolution: {integrity: sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=, tarball: http://npm.zhenguanyu.com/ora/download/ora-3.4.0.tgz}
    engines: {node: '>=6'}

  os-browserify@0.3.0:
    resolution: {integrity: sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=, tarball: http://npm.zhenguanyu.com/os-browserify/download/os-browserify-0.3.0.tgz}

  os-tmpdir@1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=, tarball: http://npm.zhenguanyu.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  p-finally@1.0.0:
    resolution: {integrity: sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=, tarball: http://npm.zhenguanyu.com/p-finally/download/p-finally-1.0.0.tgz}
    engines: {node: '>=4'}

  p-finally@2.0.1:
    resolution: {integrity: sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=, tarball: http://npm.zhenguanyu.com/p-finally/download/p-finally-2.0.1.tgz}
    engines: {node: '>=8'}

  p-limit@1.3.0:
    resolution: {integrity: sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=, tarball: http://npm.zhenguanyu.com/p-limit/download/p-limit-1.3.0.tgz}
    engines: {node: '>=4'}

  p-limit@2.3.0:
    resolution: {integrity: sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=, tarball: http://npm.zhenguanyu.com/p-limit/download/p-limit-2.3.0.tgz}
    engines: {node: '>=6'}

  p-locate@2.0.0:
    resolution: {integrity: sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=, tarball: http://npm.zhenguanyu.com/p-locate/download/p-locate-2.0.0.tgz}
    engines: {node: '>=4'}

  p-locate@3.0.0:
    resolution: {integrity: sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=, tarball: http://npm.zhenguanyu.com/p-locate/download/p-locate-3.0.0.tgz}
    engines: {node: '>=6'}

  p-locate@4.1.0:
    resolution: {integrity: sha1-o0KLtwiLOmApL2aRkni3wpetTwc=, tarball: http://npm.zhenguanyu.com/p-locate/download/p-locate-4.1.0.tgz}
    engines: {node: '>=8'}

  p-map@1.2.0:
    resolution: {integrity: sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s=, tarball: http://npm.zhenguanyu.com/p-map/download/p-map-1.2.0.tgz}
    engines: {node: '>=4'}

  p-map@2.1.0:
    resolution: {integrity: sha1-MQko/u+cnsxltosXaTAYpmXOoXU=, tarball: http://npm.zhenguanyu.com/p-map/download/p-map-2.1.0.tgz}
    engines: {node: '>=6'}

  p-retry@3.0.1:
    resolution: {integrity: sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=, tarball: http://npm.zhenguanyu.com/p-retry/download/p-retry-3.0.1.tgz}
    engines: {node: '>=6'}

  p-try@1.0.0:
    resolution: {integrity: sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=, tarball: http://npm.zhenguanyu.com/p-try/download/p-try-1.0.0.tgz}
    engines: {node: '>=4'}

  p-try@2.2.0:
    resolution: {integrity: sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=, tarball: http://npm.zhenguanyu.com/p-try/download/p-try-2.2.0.tgz}
    engines: {node: '>=6'}

  pako@1.0.11:
    resolution: {integrity: sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=, tarball: http://npm.zhenguanyu.com/pako/download/pako-1.0.11.tgz}

  parallel-transform@1.2.0:
    resolution: {integrity: sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=, tarball: http://npm.zhenguanyu.com/parallel-transform/download/parallel-transform-1.2.0.tgz}

  param-case@2.1.1:
    resolution: {integrity: sha1-35T9jPZTHs915r75oIWPvHK+Ikc=, tarball: http://npm.zhenguanyu.com/param-case/download/param-case-2.1.1.tgz}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=, tarball: http://npm.zhenguanyu.com/parent-module/download/parent-module-1.0.1.tgz}
    engines: {node: '>=6'}

  parse-asn1@5.1.7:
    resolution: {integrity: sha1-c82qqCISX5ZHFlYl60X4oFHS3wY=, tarball: http://npm.zhenguanyu.com/parse-asn1/download/parse-asn1-5.1.7.tgz}
    engines: {node: '>= 0.10'}

  parse-json@4.0.0:
    resolution: {integrity: sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=, tarball: http://npm.zhenguanyu.com/parse-json/download/parse-json-4.0.0.tgz}
    engines: {node: '>=4'}

  parse-json@5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=, tarball: http://npm.zhenguanyu.com/parse-json/download/parse-json-5.2.0.tgz}
    engines: {node: '>=8'}

  parse5-htmlparser2-tree-adapter@6.0.1:
    resolution: {integrity: sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=, tarball: http://npm.zhenguanyu.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz}

  parse5@5.1.1:
    resolution: {integrity: sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=, tarball: http://npm.zhenguanyu.com/parse5/download/parse5-5.1.1.tgz}

  parse5@6.0.1:
    resolution: {integrity: sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=, tarball: http://npm.zhenguanyu.com/parse5/download/parse5-6.0.1.tgz}

  parseurl@1.3.3:
    resolution: {integrity: sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=, tarball: http://npm.zhenguanyu.com/parseurl/download/parseurl-1.3.3.tgz}
    engines: {node: '>= 0.8'}

  pascalcase@0.1.1:
    resolution: {integrity: sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=, tarball: http://npm.zhenguanyu.com/pascalcase/download/pascalcase-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  path-browserify@0.0.1:
    resolution: {integrity: sha1-5sTd1+06onxoogzE5Q4aTug7vEo=, tarball: http://npm.zhenguanyu.com/path-browserify/download/path-browserify-0.0.1.tgz}

  path-dirname@1.0.2:
    resolution: {integrity: sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=, tarball: http://npm.zhenguanyu.com/path-dirname/download/path-dirname-1.0.2.tgz}

  path-exists@2.1.0:
    resolution: {integrity: sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=, tarball: http://npm.zhenguanyu.com/path-exists/download/path-exists-2.1.0.tgz}
    engines: {node: '>=0.10.0'}

  path-exists@3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=, tarball: http://npm.zhenguanyu.com/path-exists/download/path-exists-3.0.0.tgz}
    engines: {node: '>=4'}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=, tarball: http://npm.zhenguanyu.com/path-exists/download/path-exists-4.0.0.tgz}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=, tarball: http://npm.zhenguanyu.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  path-is-inside@1.0.2:
    resolution: {integrity: sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=, tarball: http://npm.zhenguanyu.com/path-is-inside/download/path-is-inside-1.0.2.tgz}

  path-key@2.0.1:
    resolution: {integrity: sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=, tarball: http://npm.zhenguanyu.com/path-key/download/path-key-2.0.1.tgz}
    engines: {node: '>=4'}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=, tarball: http://npm.zhenguanyu.com/path-key/download/path-key-3.1.1.tgz}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=, tarball: http://npm.zhenguanyu.com/path-parse/download/path-parse-1.0.7.tgz}

  path-to-regexp@0.1.10:
    resolution: {integrity: sha1-Z+kQjFwFUbnlMmBkOH3kdjxNX4s=, tarball: http://npm.zhenguanyu.com/path-to-regexp/download/path-to-regexp-0.1.10.tgz}

  path-type@3.0.0:
    resolution: {integrity: sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=, tarball: http://npm.zhenguanyu.com/path-type/download/path-type-3.0.0.tgz}
    engines: {node: '>=4'}

  pbkdf2@3.1.2:
    resolution: {integrity: sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=, tarball: http://npm.zhenguanyu.com/pbkdf2/download/pbkdf2-3.1.2.tgz}
    engines: {node: '>=0.12'}

  performance-now@2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=, tarball: http://npm.zhenguanyu.com/performance-now/download/performance-now-2.1.0.tgz}

  picocolors@0.2.1:
    resolution: {integrity: sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=, tarball: http://npm.zhenguanyu.com/picocolors/download/picocolors-0.2.1.tgz}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=, tarball: http://npm.zhenguanyu.com/picocolors/download/picocolors-1.1.1.tgz}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=, tarball: http://npm.zhenguanyu.com/picomatch/download/picomatch-2.3.1.tgz}
    engines: {node: '>=8.6'}

  pify@2.3.0:
    resolution: {integrity: sha1-7RQaasBDqEnqWISY59yosVMw6Qw=, tarball: http://npm.zhenguanyu.com/pify/download/pify-2.3.0.tgz}
    engines: {node: '>=0.10.0'}

  pify@3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=, tarball: http://npm.zhenguanyu.com/pify/download/pify-3.0.0.tgz}
    engines: {node: '>=4'}

  pify@4.0.1:
    resolution: {integrity: sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=, tarball: http://npm.zhenguanyu.com/pify/download/pify-4.0.1.tgz}
    engines: {node: '>=6'}

  pinkie-promise@2.0.1:
    resolution: {integrity: sha1-ITXW36ejWMBprJsXh3YogihFD/o=, tarball: http://npm.zhenguanyu.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz}
    engines: {node: '>=0.10.0'}

  pinkie@2.0.4:
    resolution: {integrity: sha1-clVrgM+g1IqXToDnckjoDtT3+HA=, tarball: http://npm.zhenguanyu.com/pinkie/download/pinkie-2.0.4.tgz}
    engines: {node: '>=0.10.0'}

  pkg-dir@1.0.0:
    resolution: {integrity: sha1-ektQio1bstYp1EcFb/TpyTFM89Q=, tarball: http://npm.zhenguanyu.com/pkg-dir/download/pkg-dir-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  pkg-dir@2.0.0:
    resolution: {integrity: sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=, tarball: http://npm.zhenguanyu.com/pkg-dir/download/pkg-dir-2.0.0.tgz}
    engines: {node: '>=4'}

  pkg-dir@3.0.0:
    resolution: {integrity: sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=, tarball: http://npm.zhenguanyu.com/pkg-dir/download/pkg-dir-3.0.0.tgz}
    engines: {node: '>=6'}

  pkg-dir@4.2.0:
    resolution: {integrity: sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=, tarball: http://npm.zhenguanyu.com/pkg-dir/download/pkg-dir-4.2.0.tgz}
    engines: {node: '>=8'}

  pkg-up@2.0.0:
    resolution: {integrity: sha1-yBmscoBZpGHKscOImivjxJoATX8=, tarball: http://npm.zhenguanyu.com/pkg-up/download/pkg-up-2.0.0.tgz}
    engines: {node: '>=4'}

  please-upgrade-node@3.2.0:
    resolution: {integrity: sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=, tarball: http://npm.zhenguanyu.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz}

  pluralize@7.0.0:
    resolution: {integrity: sha1-KYuJ34uTsCIdv0Ia0rGx6iP8Z3c=, tarball: http://npm.zhenguanyu.com/pluralize/download/pluralize-7.0.0.tgz}
    engines: {node: '>=4'}

  portfinder@1.0.32:
    resolution: {integrity: sha1-L+G55YOJcSQp3CvqW+shRhRsf4E=, tarball: http://npm.zhenguanyu.com/portfinder/download/portfinder-1.0.32.tgz}
    engines: {node: '>= 0.12.0'}

  posix-character-classes@0.1.1:
    resolution: {integrity: sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=, tarball: http://npm.zhenguanyu.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  possible-typed-array-names@1.0.0:
    resolution: {integrity: sha1-ibtjxvraLD6QrcSmR77us5zHv48=, tarball: http://npm.zhenguanyu.com/possible-typed-array-names/download/possible-typed-array-names-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  postcss-calc@7.0.5:
    resolution: {integrity: sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4=, tarball: http://npm.zhenguanyu.com/postcss-calc/download/postcss-calc-7.0.5.tgz}

  postcss-colormin@4.0.3:
    resolution: {integrity: sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=, tarball: http://npm.zhenguanyu.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz}
    engines: {node: '>=6.9.0'}

  postcss-convert-values@4.0.1:
    resolution: {integrity: sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=, tarball: http://npm.zhenguanyu.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  postcss-discard-comments@4.0.2:
    resolution: {integrity: sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=, tarball: http://npm.zhenguanyu.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-discard-duplicates@4.0.2:
    resolution: {integrity: sha1-P+EzzTyCKC5VD8myORdqkge3hOs=, tarball: http://npm.zhenguanyu.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-discard-empty@4.0.1:
    resolution: {integrity: sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=, tarball: http://npm.zhenguanyu.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  postcss-discard-overridden@4.0.1:
    resolution: {integrity: sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=, tarball: http://npm.zhenguanyu.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  postcss-load-config@2.1.2:
    resolution: {integrity: sha1-xepQTyxK7zPHNZo03jVzdyrXUCo=, tarball: http://npm.zhenguanyu.com/postcss-load-config/download/postcss-load-config-2.1.2.tgz}
    engines: {node: '>= 4'}

  postcss-loader@3.0.0:
    resolution: {integrity: sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=, tarball: http://npm.zhenguanyu.com/postcss-loader/download/postcss-loader-3.0.0.tgz}
    engines: {node: '>= 6'}

  postcss-merge-longhand@4.0.11:
    resolution: {integrity: sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=, tarball: http://npm.zhenguanyu.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz}
    engines: {node: '>=6.9.0'}

  postcss-merge-rules@4.0.3:
    resolution: {integrity: sha1-NivqT/Wh+Y5AdacTxsslrv75plA=, tarball: http://npm.zhenguanyu.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz}
    engines: {node: '>=6.9.0'}

  postcss-minify-font-values@4.0.2:
    resolution: {integrity: sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=, tarball: http://npm.zhenguanyu.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-minify-gradients@4.0.2:
    resolution: {integrity: sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=, tarball: http://npm.zhenguanyu.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-minify-params@4.0.2:
    resolution: {integrity: sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=, tarball: http://npm.zhenguanyu.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-minify-selectors@4.0.2:
    resolution: {integrity: sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=, tarball: http://npm.zhenguanyu.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-modules-extract-imports@1.2.1:
    resolution: {integrity: sha1-3IfjQUjsfqtfeR981YSYMzdbdBo=, tarball: http://npm.zhenguanyu.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.1.tgz}

  postcss-modules-local-by-default@1.2.0:
    resolution: {integrity: sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk=, tarball: http://npm.zhenguanyu.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz}

  postcss-modules-scope@1.1.0:
    resolution: {integrity: sha1-1upkmUx5+XtipytCb75gVqGUu5A=, tarball: http://npm.zhenguanyu.com/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz}

  postcss-modules-values@1.3.0:
    resolution: {integrity: sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA=, tarball: http://npm.zhenguanyu.com/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz}

  postcss-normalize-charset@4.0.1:
    resolution: {integrity: sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=, tarball: http://npm.zhenguanyu.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  postcss-normalize-display-values@4.0.2:
    resolution: {integrity: sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=, tarball: http://npm.zhenguanyu.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-normalize-positions@4.0.2:
    resolution: {integrity: sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=, tarball: http://npm.zhenguanyu.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-normalize-repeat-style@4.0.2:
    resolution: {integrity: sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=, tarball: http://npm.zhenguanyu.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-normalize-string@4.0.2:
    resolution: {integrity: sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=, tarball: http://npm.zhenguanyu.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-normalize-timing-functions@4.0.2:
    resolution: {integrity: sha1-jgCcoqOUnNr4rSPmtquZy159KNk=, tarball: http://npm.zhenguanyu.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-normalize-unicode@4.0.1:
    resolution: {integrity: sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=, tarball: http://npm.zhenguanyu.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  postcss-normalize-url@4.0.1:
    resolution: {integrity: sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=, tarball: http://npm.zhenguanyu.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  postcss-normalize-whitespace@4.0.2:
    resolution: {integrity: sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=, tarball: http://npm.zhenguanyu.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-ordered-values@4.1.2:
    resolution: {integrity: sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=, tarball: http://npm.zhenguanyu.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-reduce-initial@4.0.3:
    resolution: {integrity: sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=, tarball: http://npm.zhenguanyu.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz}
    engines: {node: '>=6.9.0'}

  postcss-reduce-transforms@4.0.2:
    resolution: {integrity: sha1-F++kBerMbge+NBSlyi0QdGgdTik=, tarball: http://npm.zhenguanyu.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz}
    engines: {node: '>=6.9.0'}

  postcss-selector-parser@3.1.2:
    resolution: {integrity: sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=, tarball: http://npm.zhenguanyu.com/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz}
    engines: {node: '>=8'}

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=, tarball: http://npm.zhenguanyu.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz}
    engines: {node: '>=4'}

  postcss-svgo@4.0.3:
    resolution: {integrity: sha1-NDos26yVBdQWJD1Jb3JPOIlMlB4=, tarball: http://npm.zhenguanyu.com/postcss-svgo/download/postcss-svgo-4.0.3.tgz}
    engines: {node: '>=6.9.0'}

  postcss-unique-selectors@4.0.1:
    resolution: {integrity: sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=, tarball: http://npm.zhenguanyu.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz}
    engines: {node: '>=6.9.0'}

  postcss-value-parser@3.3.1:
    resolution: {integrity: sha1-n/giVH4okyE88cMO+lGsX9G6goE=, tarball: http://npm.zhenguanyu.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=, tarball: http://npm.zhenguanyu.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz}

  postcss@6.0.23:
    resolution: {integrity: sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=, tarball: http://npm.zhenguanyu.com/postcss/download/postcss-6.0.23.tgz}
    engines: {node: '>=4.0.0'}

  postcss@7.0.39:
    resolution: {integrity: sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=, tarball: http://npm.zhenguanyu.com/postcss/download/postcss-7.0.39.tgz}
    engines: {node: '>=6.0.0'}

  postcss@8.4.47:
    resolution: {integrity: sha1-W/bJoBDz5yTFA78D73lH3LD+o2U=, tarball: http://npm.zhenguanyu.com/postcss/download/postcss-8.4.47.tgz}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.1.2:
    resolution: {integrity: sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=, tarball: http://npm.zhenguanyu.com/prelude-ls/download/prelude-ls-1.1.2.tgz}
    engines: {node: '>= 0.8.0'}

  prepend-http@1.0.4:
    resolution: {integrity: sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=, tarball: http://npm.zhenguanyu.com/prepend-http/download/prepend-http-1.0.4.tgz}
    engines: {node: '>=0.10.0'}

  prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=, tarball: http://npm.zhenguanyu.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz}
    engines: {node: '>=6.0.0'}

  prettier@1.19.1:
    resolution: {integrity: sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=, tarball: http://npm.zhenguanyu.com/prettier/download/prettier-1.19.1.tgz}
    engines: {node: '>=4'}
    hasBin: true

  prettier@2.8.8:
    resolution: {integrity: sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=, tarball: http://npm.zhenguanyu.com/prettier/download/prettier-2.8.8.tgz}
    engines: {node: '>=10.13.0'}
    hasBin: true

  pretty-error@2.1.2:
    resolution: {integrity: sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y=, tarball: http://npm.zhenguanyu.com/pretty-error/download/pretty-error-2.1.2.tgz}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha1-eCDZsWEgzFXKmud5JoCufbptf+I=, tarball: http://npm.zhenguanyu.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz}

  process@0.11.10:
    resolution: {integrity: sha1-czIwDoQBYb2j5podHZGn1LwW8YI=, tarball: http://npm.zhenguanyu.com/process/download/process-0.11.10.tgz}
    engines: {node: '>= 0.6.0'}

  progress@2.0.3:
    resolution: {integrity: sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=, tarball: http://npm.zhenguanyu.com/progress/download/progress-2.0.3.tgz}
    engines: {node: '>=0.4.0'}

  promise-inflight@1.0.1:
    resolution: {integrity: sha1-mEcocL8igTL8vdhoEputEsPAKeM=, tarball: http://npm.zhenguanyu.com/promise-inflight/download/promise-inflight-1.0.1.tgz}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true

  property-expr@1.5.1:
    resolution: {integrity: sha1-IuhwaJSgyOKNWHNYBPa6OjZzMU8=, tarball: http://npm.zhenguanyu.com/property-expr/download/property-expr-1.5.1.tgz}

  proxy-addr@2.0.7:
    resolution: {integrity: sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=, tarball: http://npm.zhenguanyu.com/proxy-addr/download/proxy-addr-2.0.7.tgz}
    engines: {node: '>= 0.10'}

  prr@1.0.1:
    resolution: {integrity: sha1-0/wRS6BplaRexok/SEzrHXj19HY=, tarball: http://npm.zhenguanyu.com/prr/download/prr-1.0.1.tgz}

  pseudomap@1.0.2:
    resolution: {integrity: sha1-8FKijacOYYkX7wqKw0wa5aaChrM=, tarball: http://npm.zhenguanyu.com/pseudomap/download/pseudomap-1.0.2.tgz}

  psl@1.9.0:
    resolution: {integrity: sha1-0N8qE38AeUVl/K87LADNCfjVpac=, tarball: http://npm.zhenguanyu.com/psl/download/psl-1.9.0.tgz}

  public-encrypt@4.0.3:
    resolution: {integrity: sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=, tarball: http://npm.zhenguanyu.com/public-encrypt/download/public-encrypt-4.0.3.tgz}

  pump@2.0.1:
    resolution: {integrity: sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=, tarball: http://npm.zhenguanyu.com/pump/download/pump-2.0.1.tgz}

  pump@3.0.2:
    resolution: {integrity: sha1-g28+3WvC7lmSVskk/+DYhXPdy/g=, tarball: http://npm.zhenguanyu.com/pump/download/pump-3.0.2.tgz}

  pumpify@1.5.1:
    resolution: {integrity: sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=, tarball: http://npm.zhenguanyu.com/pumpify/download/pumpify-1.5.1.tgz}

  punycode@1.4.1:
    resolution: {integrity: sha1-wNWmOycYgArY4esPpSachN1BhF4=, tarball: http://npm.zhenguanyu.com/punycode/download/punycode-1.4.1.tgz}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=, tarball: http://npm.zhenguanyu.com/punycode/download/punycode-2.3.1.tgz}
    engines: {node: '>=6'}

  q@1.5.1:
    resolution: {integrity: sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=, tarball: http://npm.zhenguanyu.com/q/download/q-1.5.1.tgz}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    deprecated: |-
      You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.

      (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)

  qs@6.13.0:
    resolution: {integrity: sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=, tarball: http://npm.zhenguanyu.com/qs/download/qs-6.13.0.tgz}
    engines: {node: '>=0.6'}

  qs@6.5.3:
    resolution: {integrity: sha1-Ou7/yRln7241wOSI70b7KWq3aq0=, tarball: http://npm.zhenguanyu.com/qs/download/qs-6.5.3.tgz}
    engines: {node: '>=0.6'}

  query-string@4.3.4:
    resolution: {integrity: sha1-u7aTucqRXCMlFbIosaArYJBD2+s=, tarball: http://npm.zhenguanyu.com/query-string/download/query-string-4.3.4.tgz}
    engines: {node: '>=0.10.0'}

  querystring-es3@0.2.1:
    resolution: {integrity: sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=, tarball: http://npm.zhenguanyu.com/querystring-es3/download/querystring-es3-0.2.1.tgz}
    engines: {node: '>=0.4.x'}

  querystringify@2.2.0:
    resolution: {integrity: sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=, tarball: http://npm.zhenguanyu.com/querystringify/download/querystringify-2.2.0.tgz}

  randombytes@2.1.0:
    resolution: {integrity: sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=, tarball: http://npm.zhenguanyu.com/randombytes/download/randombytes-2.1.0.tgz}

  randomfill@1.0.4:
    resolution: {integrity: sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=, tarball: http://npm.zhenguanyu.com/randomfill/download/randomfill-1.0.4.tgz}

  range-parser@1.2.1:
    resolution: {integrity: sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=, tarball: http://npm.zhenguanyu.com/range-parser/download/range-parser-1.2.1.tgz}
    engines: {node: '>= 0.6'}

  raw-body@2.5.2:
    resolution: {integrity: sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=, tarball: http://npm.zhenguanyu.com/raw-body/download/raw-body-2.5.2.tgz}
    engines: {node: '>= 0.8'}

  read-pkg@5.2.0:
    resolution: {integrity: sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=, tarball: http://npm.zhenguanyu.com/read-pkg/download/read-pkg-5.2.0.tgz}
    engines: {node: '>=8'}

  readable-stream@2.3.8:
    resolution: {integrity: sha1-kRJegEK7obmIf0k0X2J3Anzovps=, tarball: http://npm.zhenguanyu.com/readable-stream/download/readable-stream-2.3.8.tgz}

  readable-stream@3.6.2:
    resolution: {integrity: sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=, tarball: http://npm.zhenguanyu.com/readable-stream/download/readable-stream-3.6.2.tgz}
    engines: {node: '>= 6'}

  readdirp@2.2.1:
    resolution: {integrity: sha1-DodiKjMlqjPokihcr4tOhGUppSU=, tarball: http://npm.zhenguanyu.com/readdirp/download/readdirp-2.2.1.tgz}
    engines: {node: '>=0.10'}

  readdirp@3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=, tarball: http://npm.zhenguanyu.com/readdirp/download/readdirp-3.6.0.tgz}
    engines: {node: '>=8.10.0'}

  regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=, tarball: http://npm.zhenguanyu.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=, tarball: http://npm.zhenguanyu.com/regenerate/download/regenerate-1.4.2.tgz}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha1-NWreECY/aF3aElEAzYYsHbiVMn8=, tarball: http://npm.zhenguanyu.com/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz}

  regenerator-transform@0.15.2:
    resolution: {integrity: sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=, tarball: http://npm.zhenguanyu.com/regenerator-transform/download/regenerator-transform-0.15.2.tgz}

  regex-not@1.0.2:
    resolution: {integrity: sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=, tarball: http://npm.zhenguanyu.com/regex-not/download/regex-not-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  regexp.prototype.flags@1.5.3:
    resolution: {integrity: sha1-s65AsdJJm4NQqyw/5u84RdOpb0I=, tarball: http://npm.zhenguanyu.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.3.tgz}
    engines: {node: '>= 0.4'}

  regexpp@1.1.0:
    resolution: {integrity: sha1-DjUW3Qt5BPQT0tQZPc5GGMOmias=, tarball: http://npm.zhenguanyu.com/regexpp/download/regexpp-1.1.0.tgz}
    engines: {node: '>=4.0.0'}

  regexpp@2.0.1:
    resolution: {integrity: sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=, tarball: http://npm.zhenguanyu.com/regexpp/download/regexpp-2.0.1.tgz}
    engines: {node: '>=6.5.0'}

  regexpu-core@6.1.1:
    resolution: {integrity: sha1-tGmyRVlMstCIzuvGNp3OuMAL7Kw=, tarball: http://npm.zhenguanyu.com/regexpu-core/download/regexpu-core-6.1.1.tgz}
    engines: {node: '>=4'}

  regjsgen@0.8.0:
    resolution: {integrity: sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=, tarball: http://npm.zhenguanyu.com/regjsgen/download/regjsgen-0.8.0.tgz}

  regjsparser@0.11.2:
    resolution: {integrity: sha1-dAStQr4AIm1yvPHwA/H0QYYZE9g=, tarball: http://npm.zhenguanyu.com/regjsparser/download/regjsparser-0.11.2.tgz}
    hasBin: true

  relateurl@0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=, tarball: http://npm.zhenguanyu.com/relateurl/download/relateurl-0.2.7.tgz}
    engines: {node: '>= 0.10'}

  remove-trailing-separator@1.1.0:
    resolution: {integrity: sha1-wkvOKig62tW8P1jg1IJJuSN52O8=, tarball: http://npm.zhenguanyu.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz}

  renderkid@2.0.7:
    resolution: {integrity: sha1-Rk8namvc7mBvShWZP5sp/HTKhgk=, tarball: http://npm.zhenguanyu.com/renderkid/download/renderkid-2.0.7.tgz}

  repeat-element@1.1.4:
    resolution: {integrity: sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=, tarball: http://npm.zhenguanyu.com/repeat-element/download/repeat-element-1.1.4.tgz}
    engines: {node: '>=0.10.0'}

  repeat-string@1.6.1:
    resolution: {integrity: sha1-jcrkcOHIirwtYA//Sndihtp15jc=, tarball: http://npm.zhenguanyu.com/repeat-string/download/repeat-string-1.6.1.tgz}
    engines: {node: '>=0.10'}

  request-promise-core@1.1.4:
    resolution: {integrity: sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=, tarball: http://npm.zhenguanyu.com/request-promise-core/download/request-promise-core-1.1.4.tgz}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      request: ^2.34

  request-promise-native@1.0.9:
    resolution: {integrity: sha1-5AcSBSal79yaObKKVnm/R7nZ3Cg=, tarball: http://npm.zhenguanyu.com/request-promise-native/download/request-promise-native-1.0.9.tgz}
    engines: {node: '>=0.12.0'}
    deprecated: request-promise-native has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142
    peerDependencies:
      request: ^2.34

  request@2.88.2:
    resolution: {integrity: sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=, tarball: http://npm.zhenguanyu.com/request/download/request-2.88.2.tgz}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142

  require-directory@2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=, tarball: http://npm.zhenguanyu.com/require-directory/download/require-directory-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  require-main-filename@2.0.0:
    resolution: {integrity: sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=, tarball: http://npm.zhenguanyu.com/require-main-filename/download/require-main-filename-2.0.0.tgz}

  require-uncached@1.0.3:
    resolution: {integrity: sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=, tarball: http://npm.zhenguanyu.com/require-uncached/download/require-uncached-1.0.3.tgz}
    engines: {node: '>=0.10.0'}

  requires-port@1.0.0:
    resolution: {integrity: sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=, tarball: http://npm.zhenguanyu.com/requires-port/download/requires-port-1.0.0.tgz}

  reselect@3.0.1:
    resolution: {integrity: sha1-79qpjqdFEyTQkrKyFjpqHXqaIUc=, tarball: http://npm.zhenguanyu.com/reselect/download/reselect-3.0.1.tgz}

  resolve-cwd@2.0.0:
    resolution: {integrity: sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=, tarball: http://npm.zhenguanyu.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz}
    engines: {node: '>=4'}

  resolve-from@1.0.1:
    resolution: {integrity: sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=, tarball: http://npm.zhenguanyu.com/resolve-from/download/resolve-from-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  resolve-from@3.0.0:
    resolution: {integrity: sha1-six699nWiBvItuZTM17rywoYh0g=, tarball: http://npm.zhenguanyu.com/resolve-from/download/resolve-from-3.0.0.tgz}
    engines: {node: '>=4'}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=, tarball: http://npm.zhenguanyu.com/resolve-from/download/resolve-from-4.0.0.tgz}
    engines: {node: '>=4'}

  resolve-url@0.2.1:
    resolution: {integrity: sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=, tarball: http://npm.zhenguanyu.com/resolve-url/download/resolve-url-0.2.1.tgz}
    deprecated: https://github.com/lydell/resolve-url#deprecated

  resolve@1.22.8:
    resolution: {integrity: sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0=, tarball: http://npm.zhenguanyu.com/resolve/download/resolve-1.22.8.tgz}
    hasBin: true

  restore-cursor@2.0.0:
    resolution: {integrity: sha1-n37ih/gv0ybU/RYpI9YhKe7g368=, tarball: http://npm.zhenguanyu.com/restore-cursor/download/restore-cursor-2.0.0.tgz}
    engines: {node: '>=4'}

  ret@0.1.15:
    resolution: {integrity: sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=, tarball: http://npm.zhenguanyu.com/ret/download/ret-0.1.15.tgz}
    engines: {node: '>=0.12'}

  retry@0.12.0:
    resolution: {integrity: sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=, tarball: http://npm.zhenguanyu.com/retry/download/retry-0.12.0.tgz}
    engines: {node: '>= 4'}

  rgb-regex@1.0.1:
    resolution: {integrity: sha1-wODWiC3w4jviVKR16O3UGRX+rrE=, tarball: http://npm.zhenguanyu.com/rgb-regex/download/rgb-regex-1.0.1.tgz}

  rgba-regex@1.0.0:
    resolution: {integrity: sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=, tarball: http://npm.zhenguanyu.com/rgba-regex/download/rgba-regex-1.0.0.tgz}

  rimraf@2.6.3:
    resolution: {integrity: sha1-stEE/g2Psnz54KHNqCYt04M8bKs=, tarball: http://npm.zhenguanyu.com/rimraf/download/rimraf-2.6.3.tgz}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@2.7.1:
    resolution: {integrity: sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=, tarball: http://npm.zhenguanyu.com/rimraf/download/rimraf-2.7.1.tgz}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  ripemd160@2.0.2:
    resolution: {integrity: sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=, tarball: http://npm.zhenguanyu.com/ripemd160/download/ripemd160-2.0.2.tgz}

  run-async@2.4.1:
    resolution: {integrity: sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=, tarball: http://npm.zhenguanyu.com/run-async/download/run-async-2.4.1.tgz}
    engines: {node: '>=0.12.0'}

  run-queue@1.0.3:
    resolution: {integrity: sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=, tarball: http://npm.zhenguanyu.com/run-queue/download/run-queue-1.0.3.tgz}

  rx-lite-aggregates@4.0.8:
    resolution: {integrity: sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=, tarball: http://npm.zhenguanyu.com/rx-lite-aggregates/download/rx-lite-aggregates-4.0.8.tgz}

  rx-lite@4.0.8:
    resolution: {integrity: sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=, tarball: http://npm.zhenguanyu.com/rx-lite/download/rx-lite-4.0.8.tgz}

  rxjs@6.6.7:
    resolution: {integrity: sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=, tarball: http://npm.zhenguanyu.com/rxjs/download/rxjs-6.6.7.tgz}
    engines: {npm: '>=2.0.0'}

  safe-array-concat@1.1.2:
    resolution: {integrity: sha1-gdd+4MTouGNjUifHISeN1STCDts=, tarball: http://npm.zhenguanyu.com/safe-array-concat/download/safe-array-concat-1.1.2.tgz}
    engines: {node: '>=0.4'}

  safe-buffer@5.1.2:
    resolution: {integrity: sha1-mR7GnSluAxN0fVm9/St0XDX4go0=, tarball: http://npm.zhenguanyu.com/safe-buffer/download/safe-buffer-5.1.2.tgz}

  safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=, tarball: http://npm.zhenguanyu.com/safe-buffer/download/safe-buffer-5.2.1.tgz}

  safe-regex-test@1.0.3:
    resolution: {integrity: sha1-pbTA8G4KtQ6iw5XBTYNxIykkw3c=, tarball: http://npm.zhenguanyu.com/safe-regex-test/download/safe-regex-test-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  safe-regex@1.1.0:
    resolution: {integrity: sha1-QKNmnzsHfR6UPURinhV91IAjvy4=, tarball: http://npm.zhenguanyu.com/safe-regex/download/safe-regex-1.1.0.tgz}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=, tarball: http://npm.zhenguanyu.com/safer-buffer/download/safer-buffer-2.1.2.tgz}

  sax@1.2.4:
    resolution: {integrity: sha1-KBYjTiN4vdxOU1T6tcqold9xANk=, tarball: http://npm.zhenguanyu.com/sax/download/sax-1.2.4.tgz}

  schema-utils@0.4.7:
    resolution: {integrity: sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=, tarball: http://npm.zhenguanyu.com/schema-utils/download/schema-utils-0.4.7.tgz}
    engines: {node: '>= 4'}

  schema-utils@1.0.0:
    resolution: {integrity: sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=, tarball: http://npm.zhenguanyu.com/schema-utils/download/schema-utils-1.0.0.tgz}
    engines: {node: '>= 4'}

  schema-utils@2.7.1:
    resolution: {integrity: sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=, tarball: http://npm.zhenguanyu.com/schema-utils/download/schema-utils-2.7.1.tgz}
    engines: {node: '>= 8.9.0'}

  select-hose@2.0.0:
    resolution: {integrity: sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=, tarball: http://npm.zhenguanyu.com/select-hose/download/select-hose-2.0.0.tgz}

  selfsigned@1.10.14:
    resolution: {integrity: sha1-7lHYTZ3OzGHgfkq6NPIpq1JcFXQ=, tarball: http://npm.zhenguanyu.com/selfsigned/download/selfsigned-1.10.14.tgz}

  semver-compare@1.0.0:
    resolution: {integrity: sha1-De4hahyUGrN+nvsXiPavxf9VN/w=, tarball: http://npm.zhenguanyu.com/semver-compare/download/semver-compare-1.0.0.tgz}

  semver@5.7.2:
    resolution: {integrity: sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=, tarball: http://npm.zhenguanyu.com/semver/download/semver-5.7.2.tgz}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=, tarball: http://npm.zhenguanyu.com/semver/download/semver-6.3.1.tgz}
    hasBin: true

  send@0.19.0:
    resolution: {integrity: sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=, tarball: http://npm.zhenguanyu.com/send/download/send-0.19.0.tgz}
    engines: {node: '>= 0.8.0'}

  serialize-javascript@1.9.1:
    resolution: {integrity: sha1-z8IArvd7YAxH2pu4FJyUPnmML9s=, tarball: http://npm.zhenguanyu.com/serialize-javascript/download/serialize-javascript-1.9.1.tgz}

  serialize-javascript@4.0.0:
    resolution: {integrity: sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=, tarball: http://npm.zhenguanyu.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz}

  serve-index@1.9.1:
    resolution: {integrity: sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=, tarball: http://npm.zhenguanyu.com/serve-index/download/serve-index-1.9.1.tgz}
    engines: {node: '>= 0.8.0'}

  serve-static@1.16.2:
    resolution: {integrity: sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=, tarball: http://npm.zhenguanyu.com/serve-static/download/serve-static-1.16.2.tgz}
    engines: {node: '>= 0.8.0'}

  set-blocking@2.0.0:
    resolution: {integrity: sha1-BF+XgtARrppoA93TgrJDkrPYkPc=, tarball: http://npm.zhenguanyu.com/set-blocking/download/set-blocking-2.0.0.tgz}

  set-function-length@1.2.2:
    resolution: {integrity: sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=, tarball: http://npm.zhenguanyu.com/set-function-length/download/set-function-length-1.2.2.tgz}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha1-FqcFxaDcL15jjKltiozU4cK5CYU=, tarball: http://npm.zhenguanyu.com/set-function-name/download/set-function-name-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  set-value@2.0.1:
    resolution: {integrity: sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=, tarball: http://npm.zhenguanyu.com/set-value/download/set-value-2.0.1.tgz}
    engines: {node: '>=0.10.0'}

  setimmediate@1.0.5:
    resolution: {integrity: sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=, tarball: http://npm.zhenguanyu.com/setimmediate/download/setimmediate-1.0.5.tgz}

  setprototypeof@1.1.0:
    resolution: {integrity: sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=, tarball: http://npm.zhenguanyu.com/setprototypeof/download/setprototypeof-1.1.0.tgz}

  setprototypeof@1.2.0:
    resolution: {integrity: sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=, tarball: http://npm.zhenguanyu.com/setprototypeof/download/setprototypeof-1.2.0.tgz}

  sha.js@2.4.11:
    resolution: {integrity: sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=, tarball: http://npm.zhenguanyu.com/sha.js/download/sha.js-2.4.11.tgz}
    hasBin: true

  shebang-command@1.2.0:
    resolution: {integrity: sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=, tarball: http://npm.zhenguanyu.com/shebang-command/download/shebang-command-1.2.0.tgz}
    engines: {node: '>=0.10.0'}

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=, tarball: http://npm.zhenguanyu.com/shebang-command/download/shebang-command-2.0.0.tgz}
    engines: {node: '>=8'}

  shebang-regex@1.0.0:
    resolution: {integrity: sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=, tarball: http://npm.zhenguanyu.com/shebang-regex/download/shebang-regex-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=, tarball: http://npm.zhenguanyu.com/shebang-regex/download/shebang-regex-3.0.0.tgz}
    engines: {node: '>=8'}

  shell-quote@1.8.1:
    resolution: {integrity: sha1-bb9Nt1UVrVusY7TxiUw6FUx2ZoA=, tarball: http://npm.zhenguanyu.com/shell-quote/download/shell-quote-1.8.1.tgz}

  side-channel@1.0.6:
    resolution: {integrity: sha1-q9Jft80kuvRUZkBrEJa3gxySFfI=, tarball: http://npm.zhenguanyu.com/side-channel/download/side-channel-1.0.6.tgz}
    engines: {node: '>= 0.4'}

  signal-exit@3.0.7:
    resolution: {integrity: sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=, tarball: http://npm.zhenguanyu.com/signal-exit/download/signal-exit-3.0.7.tgz}

  simple-git@1.132.0:
    resolution: {integrity: sha1-U6xMXsnnTjfC/UYeIzCfIvzfCbE=, tarball: http://npm.zhenguanyu.com/simple-git/download/simple-git-1.132.0.tgz}

  simple-swizzle@0.2.2:
    resolution: {integrity: sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=, tarball: http://npm.zhenguanyu.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz}

  slash@1.0.0:
    resolution: {integrity: sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=, tarball: http://npm.zhenguanyu.com/slash/download/slash-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  slash@2.0.0:
    resolution: {integrity: sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=, tarball: http://npm.zhenguanyu.com/slash/download/slash-2.0.0.tgz}
    engines: {node: '>=6'}

  slice-ansi@0.0.4:
    resolution: {integrity: sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=, tarball: http://npm.zhenguanyu.com/slice-ansi/download/slice-ansi-0.0.4.tgz}
    engines: {node: '>=0.10.0'}

  slice-ansi@1.0.0:
    resolution: {integrity: sha1-BE8aSdiEL/MHqta1Be0Xi9lQE00=, tarball: http://npm.zhenguanyu.com/slice-ansi/download/slice-ansi-1.0.0.tgz}
    engines: {node: '>=4'}

  slice-ansi@2.1.0:
    resolution: {integrity: sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=, tarball: http://npm.zhenguanyu.com/slice-ansi/download/slice-ansi-2.1.0.tgz}
    engines: {node: '>=6'}

  snapdragon-node@2.1.1:
    resolution: {integrity: sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=, tarball: http://npm.zhenguanyu.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  snapdragon-util@3.0.1:
    resolution: {integrity: sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=, tarball: http://npm.zhenguanyu.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz}
    engines: {node: '>=0.10.0'}

  snapdragon@0.8.2:
    resolution: {integrity: sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=, tarball: http://npm.zhenguanyu.com/snapdragon/download/snapdragon-0.8.2.tgz}
    engines: {node: '>=0.10.0'}

  sockjs-client@1.6.1:
    resolution: {integrity: sha1-NQuO2kLW1S3cAww5lDNkwR3K2AY=, tarball: http://npm.zhenguanyu.com/sockjs-client/download/sockjs-client-1.6.1.tgz}
    engines: {node: '>=12'}

  sockjs@0.3.24:
    resolution: {integrity: sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=, tarball: http://npm.zhenguanyu.com/sockjs/download/sockjs-0.3.24.tgz}

  sort-keys@1.1.2:
    resolution: {integrity: sha1-RBttTTRnmPG05J6JIK37oOVD+a0=, tarball: http://npm.zhenguanyu.com/sort-keys/download/sort-keys-1.1.2.tgz}
    engines: {node: '>=0.10.0'}

  source-list-map@2.0.1:
    resolution: {integrity: sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=, tarball: http://npm.zhenguanyu.com/source-list-map/download/source-list-map-2.0.1.tgz}

  source-map-js@1.2.1:
    resolution: {integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=, tarball: http://npm.zhenguanyu.com/source-map-js/download/source-map-js-1.2.1.tgz}
    engines: {node: '>=0.10.0'}

  source-map-resolve@0.5.3:
    resolution: {integrity: sha1-GQhmvs51U+H48mei7oLGBrVQmho=, tarball: http://npm.zhenguanyu.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz}
    deprecated: See https://github.com/lydell/source-map-resolve#deprecated

  source-map-support@0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=, tarball: http://npm.zhenguanyu.com/source-map-support/download/source-map-support-0.5.21.tgz}

  source-map-url@0.4.1:
    resolution: {integrity: sha1-CvZmBadFpaL5HPG7+KevvCg97FY=, tarball: http://npm.zhenguanyu.com/source-map-url/download/source-map-url-0.4.1.tgz}
    deprecated: See https://github.com/lydell/source-map-url#deprecated

  source-map@0.5.7:
    resolution: {integrity: sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=, tarball: http://npm.zhenguanyu.com/source-map/download/source-map-0.5.7.tgz}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=, tarball: http://npm.zhenguanyu.com/source-map/download/source-map-0.6.1.tgz}
    engines: {node: '>=0.10.0'}

  spdx-correct@3.2.0:
    resolution: {integrity: sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=, tarball: http://npm.zhenguanyu.com/spdx-correct/download/spdx-correct-3.2.0.tgz}

  spdx-exceptions@2.5.0:
    resolution: {integrity: sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=, tarball: http://npm.zhenguanyu.com/spdx-exceptions/download/spdx-exceptions-2.5.0.tgz}

  spdx-expression-parse@3.0.1:
    resolution: {integrity: sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=, tarball: http://npm.zhenguanyu.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz}

  spdx-license-ids@3.0.20:
    resolution: {integrity: sha1-5E7RntMY3R5YiPkzJc7oAPD1G4k=, tarball: http://npm.zhenguanyu.com/spdx-license-ids/download/spdx-license-ids-3.0.20.tgz}

  spdy-transport@3.0.0:
    resolution: {integrity: sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=, tarball: http://npm.zhenguanyu.com/spdy-transport/download/spdy-transport-3.0.0.tgz}

  spdy@4.0.2:
    resolution: {integrity: sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=, tarball: http://npm.zhenguanyu.com/spdy/download/spdy-4.0.2.tgz}
    engines: {node: '>=6.0.0'}

  split-string@3.1.0:
    resolution: {integrity: sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=, tarball: http://npm.zhenguanyu.com/split-string/download/split-string-3.1.0.tgz}
    engines: {node: '>=0.10.0'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=, tarball: http://npm.zhenguanyu.com/sprintf-js/download/sprintf-js-1.0.3.tgz}

  sshpk@1.18.0:
    resolution: {integrity: sha1-FmPlXN301oi4aka3fw1f42OroCg=, tarball: http://npm.zhenguanyu.com/sshpk/download/sshpk-1.18.0.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  ssr-window@1.0.1:
    resolution: {integrity: sha1-MHUqakZm53Z/C35qpvwv29DZs2k=, tarball: http://npm.zhenguanyu.com/ssr-window/download/ssr-window-1.0.1.tgz}

  ssr-window@2.0.0:
    resolution: {integrity: sha1-mMMBrvmVIzF/jWlhjwAQeRCW78Q=, tarball: http://npm.zhenguanyu.com/ssr-window/download/ssr-window-2.0.0.tgz}

  ssri@5.3.0:
    resolution: {integrity: sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY=, tarball: http://npm.zhenguanyu.com/ssri/download/ssri-5.3.0.tgz}

  ssri@6.0.2:
    resolution: {integrity: sha1-FXk5E08gRk5zAd26PpD/qPdyisU=, tarball: http://npm.zhenguanyu.com/ssri/download/ssri-6.0.2.tgz}

  stable@0.1.8:
    resolution: {integrity: sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=, tarball: http://npm.zhenguanyu.com/stable/download/stable-0.1.8.tgz}
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'

  stackframe@1.3.4:
    resolution: {integrity: sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=, tarball: http://npm.zhenguanyu.com/stackframe/download/stackframe-1.3.4.tgz}

  staged-git-files@1.1.2:
    resolution: {integrity: sha1-QybTOIbcns+immGTv1EbqQpGRUs=, tarball: http://npm.zhenguanyu.com/staged-git-files/download/staged-git-files-1.1.2.tgz}
    hasBin: true

  static-extend@0.1.2:
    resolution: {integrity: sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=, tarball: http://npm.zhenguanyu.com/static-extend/download/static-extend-0.1.2.tgz}
    engines: {node: '>=0.10.0'}

  statuses@1.5.0:
    resolution: {integrity: sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=, tarball: http://npm.zhenguanyu.com/statuses/download/statuses-1.5.0.tgz}
    engines: {node: '>= 0.6'}

  statuses@2.0.1:
    resolution: {integrity: sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=, tarball: http://npm.zhenguanyu.com/statuses/download/statuses-2.0.1.tgz}
    engines: {node: '>= 0.8'}

  stealthy-require@1.1.1:
    resolution: {integrity: sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=, tarball: http://npm.zhenguanyu.com/stealthy-require/download/stealthy-require-1.1.1.tgz}
    engines: {node: '>=0.10.0'}

  stream-browserify@2.0.2:
    resolution: {integrity: sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=, tarball: http://npm.zhenguanyu.com/stream-browserify/download/stream-browserify-2.0.2.tgz}

  stream-each@1.2.3:
    resolution: {integrity: sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=, tarball: http://npm.zhenguanyu.com/stream-each/download/stream-each-1.2.3.tgz}

  stream-http@2.8.3:
    resolution: {integrity: sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=, tarball: http://npm.zhenguanyu.com/stream-http/download/stream-http-2.8.3.tgz}

  stream-shift@1.0.3:
    resolution: {integrity: sha1-hbj6tNcQEPw7qHcugEbMSbijhks=, tarball: http://npm.zhenguanyu.com/stream-shift/download/stream-shift-1.0.3.tgz}

  strict-uri-encode@1.1.0:
    resolution: {integrity: sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=, tarball: http://npm.zhenguanyu.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz}
    engines: {node: '>=0.10.0'}

  string-argv@0.0.2:
    resolution: {integrity: sha1-2sMECGkMIfPDYwo/86BYd73L1zY=, tarball: http://npm.zhenguanyu.com/string-argv/download/string-argv-0.0.2.tgz}
    engines: {node: '>=0.6.19'}

  string-width@1.0.2:
    resolution: {integrity: sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=, tarball: http://npm.zhenguanyu.com/string-width/download/string-width-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  string-width@2.1.1:
    resolution: {integrity: sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=, tarball: http://npm.zhenguanyu.com/string-width/download/string-width-2.1.1.tgz}
    engines: {node: '>=4'}

  string-width@3.1.0:
    resolution: {integrity: sha1-InZ74htirxCBV0MG9prFG2IgOWE=, tarball: http://npm.zhenguanyu.com/string-width/download/string-width-3.1.0.tgz}
    engines: {node: '>=6'}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=, tarball: http://npm.zhenguanyu.com/string-width/download/string-width-4.2.3.tgz}
    engines: {node: '>=8'}

  string.prototype.padend@3.1.6:
    resolution: {integrity: sha1-unnPiZJgmpHIctqkfGuxRO5/YqU=, tarball: http://npm.zhenguanyu.com/string.prototype.padend/download/string.prototype.padend-3.1.6.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.padstart@3.1.6:
    resolution: {integrity: sha1-vaOygJgnDh4oXggxjketU7xgH/0=, tarball: http://npm.zhenguanyu.com/string.prototype.padstart/download/string.prototype.padstart-3.1.6.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.trim@1.2.9:
    resolution: {integrity: sha1-tvoybXLSx4tt8C93Wcc/j2J0+qQ=, tarball: http://npm.zhenguanyu.com/string.prototype.trim/download/string.prototype.trim-1.2.9.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.8:
    resolution: {integrity: sha1-NlG4UTcZ6Kn0jefy93ZAsmZSsik=, tarball: http://npm.zhenguanyu.com/string.prototype.trimend/download/string.prototype.trimend-1.0.8.tgz}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=, tarball: http://npm.zhenguanyu.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz}
    engines: {node: '>= 0.4'}

  string_decoder@1.1.1:
    resolution: {integrity: sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=, tarball: http://npm.zhenguanyu.com/string_decoder/download/string_decoder-1.1.1.tgz}

  string_decoder@1.3.0:
    resolution: {integrity: sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=, tarball: http://npm.zhenguanyu.com/string_decoder/download/string_decoder-1.3.0.tgz}

  stringify-object@3.3.0:
    resolution: {integrity: sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=, tarball: http://npm.zhenguanyu.com/stringify-object/download/stringify-object-3.3.0.tgz}
    engines: {node: '>=4'}

  strip-ansi@3.0.1:
    resolution: {integrity: sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=, tarball: http://npm.zhenguanyu.com/strip-ansi/download/strip-ansi-3.0.1.tgz}
    engines: {node: '>=0.10.0'}

  strip-ansi@4.0.0:
    resolution: {integrity: sha1-qEeQIusaw2iocTibY1JixQXuNo8=, tarball: http://npm.zhenguanyu.com/strip-ansi/download/strip-ansi-4.0.0.tgz}
    engines: {node: '>=4'}

  strip-ansi@5.2.0:
    resolution: {integrity: sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=, tarball: http://npm.zhenguanyu.com/strip-ansi/download/strip-ansi-5.2.0.tgz}
    engines: {node: '>=6'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=, tarball: http://npm.zhenguanyu.com/strip-ansi/download/strip-ansi-6.0.1.tgz}
    engines: {node: '>=8'}

  strip-bom@3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=, tarball: http://npm.zhenguanyu.com/strip-bom/download/strip-bom-3.0.0.tgz}
    engines: {node: '>=4'}

  strip-eof@1.0.0:
    resolution: {integrity: sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=, tarball: http://npm.zhenguanyu.com/strip-eof/download/strip-eof-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=, tarball: http://npm.zhenguanyu.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz}
    engines: {node: '>=6'}

  strip-json-comments@2.0.1:
    resolution: {integrity: sha1-PFMZQukIwml8DsNEhYwobHygpgo=, tarball: http://npm.zhenguanyu.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz}
    engines: {node: '>=0.10.0'}

  stylehacks@4.0.3:
    resolution: {integrity: sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=, tarball: http://npm.zhenguanyu.com/stylehacks/download/stylehacks-4.0.3.tgz}
    engines: {node: '>=6.9.0'}

  supports-color@2.0.0:
    resolution: {integrity: sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=, tarball: http://npm.zhenguanyu.com/supports-color/download/supports-color-2.0.0.tgz}
    engines: {node: '>=0.8.0'}

  supports-color@5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=, tarball: http://npm.zhenguanyu.com/supports-color/download/supports-color-5.5.0.tgz}
    engines: {node: '>=4'}

  supports-color@6.1.0:
    resolution: {integrity: sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=, tarball: http://npm.zhenguanyu.com/supports-color/download/supports-color-6.1.0.tgz}
    engines: {node: '>=6'}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=, tarball: http://npm.zhenguanyu.com/supports-color/download/supports-color-7.2.0.tgz}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=, tarball: http://npm.zhenguanyu.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  svg-tags@1.0.0:
    resolution: {integrity: sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=, tarball: http://npm.zhenguanyu.com/svg-tags/download/svg-tags-1.0.0.tgz}

  svgo@1.3.2:
    resolution: {integrity: sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc=, tarball: http://npm.zhenguanyu.com/svgo/download/svgo-1.3.2.tgz}
    engines: {node: '>=4.0.0'}
    deprecated: This SVGO version is no longer supported. Upgrade to v2.x.x.
    hasBin: true

  swiper@4.5.1:
    resolution: {integrity: sha1-7UOZjngM60eGEAecjSP9Ql7KY28=, tarball: http://npm.zhenguanyu.com/swiper/download/swiper-4.5.1.tgz}
    engines: {node: '>= 4.7.0'}

  symbol-observable@1.2.0:
    resolution: {integrity: sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=, tarball: http://npm.zhenguanyu.com/symbol-observable/download/symbol-observable-1.2.0.tgz}
    engines: {node: '>=0.10.0'}

  synchronous-promise@2.0.17:
    resolution: {integrity: sha1-OJATGWMvlGyYIVJYbyyvjdwlwDI=, tarball: http://npm.zhenguanyu.com/synchronous-promise/download/synchronous-promise-2.0.17.tgz}

  table@4.0.2:
    resolution: {integrity: sha1-ozRHN1OR52atNNNIbm4q7chNLjY=, tarball: http://npm.zhenguanyu.com/table/download/table-4.0.2.tgz}

  table@5.4.6:
    resolution: {integrity: sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=, tarball: http://npm.zhenguanyu.com/table/download/table-5.4.6.tgz}
    engines: {node: '>=6.0.0'}

  tapable@1.1.3:
    resolution: {integrity: sha1-ofzMBrWNth/XpF2i2kT186Pme6I=, tarball: http://npm.zhenguanyu.com/tapable/download/tapable-1.1.3.tgz}
    engines: {node: '>=6'}

  terser-webpack-plugin@1.4.6:
    resolution: {integrity: sha1-h/y2WT/RyXfNCeVhQ+zTFARgB1U=, tarball: http://npm.zhenguanyu.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.6.tgz}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0

  terser@4.8.1:
    resolution: {integrity: sha1-oA5WNFYt4iOf1ATGSQUb9vwhFE8=, tarball: http://npm.zhenguanyu.com/terser/download/terser-4.8.1.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true

  text-table@0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=, tarball: http://npm.zhenguanyu.com/text-table/download/text-table-0.2.0.tgz}

  thenify-all@1.6.0:
    resolution: {integrity: sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=, tarball: http://npm.zhenguanyu.com/thenify-all/download/thenify-all-1.6.0.tgz}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=, tarball: http://npm.zhenguanyu.com/thenify/download/thenify-3.3.1.tgz}

  thread-loader@2.1.3:
    resolution: {integrity: sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo=, tarball: http://npm.zhenguanyu.com/thread-loader/download/thread-loader-2.1.3.tgz}
    engines: {node: '>= 6.9.0 <7.0.0 || >= 8.9.0'}
    peerDependencies:
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0

  through2@2.0.5:
    resolution: {integrity: sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=, tarball: http://npm.zhenguanyu.com/through2/download/through2-2.0.5.tgz}

  through@2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=, tarball: http://npm.zhenguanyu.com/through/download/through-2.3.8.tgz}

  thunky@1.1.0:
    resolution: {integrity: sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=, tarball: http://npm.zhenguanyu.com/thunky/download/thunky-1.1.0.tgz}

  timers-browserify@2.0.12:
    resolution: {integrity: sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=, tarball: http://npm.zhenguanyu.com/timers-browserify/download/timers-browserify-2.0.12.tgz}
    engines: {node: '>=0.6.0'}

  timsort@0.3.0:
    resolution: {integrity: sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=, tarball: http://npm.zhenguanyu.com/timsort/download/timsort-0.3.0.tgz}

  tmp@0.0.33:
    resolution: {integrity: sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=, tarball: http://npm.zhenguanyu.com/tmp/download/tmp-0.0.33.tgz}
    engines: {node: '>=0.6.0'}

  to-arraybuffer@1.0.1:
    resolution: {integrity: sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=, tarball: http://npm.zhenguanyu.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz}

  to-object-path@0.3.0:
    resolution: {integrity: sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=, tarball: http://npm.zhenguanyu.com/to-object-path/download/to-object-path-0.3.0.tgz}
    engines: {node: '>=0.10.0'}

  to-regex-range@2.1.1:
    resolution: {integrity: sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=, tarball: http://npm.zhenguanyu.com/to-regex-range/download/to-regex-range-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=, tarball: http://npm.zhenguanyu.com/to-regex-range/download/to-regex-range-5.0.1.tgz}
    engines: {node: '>=8.0'}

  to-regex@3.0.2:
    resolution: {integrity: sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=, tarball: http://npm.zhenguanyu.com/to-regex/download/to-regex-3.0.2.tgz}
    engines: {node: '>=0.10.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=, tarball: http://npm.zhenguanyu.com/toidentifier/download/toidentifier-1.0.1.tgz}
    engines: {node: '>=0.6'}

  toposort@1.0.7:
    resolution: {integrity: sha1-LmhELZ9k7HILjMieZEOsbKqVACk=, tarball: http://npm.zhenguanyu.com/toposort/download/toposort-1.0.7.tgz}

  toposort@2.0.2:
    resolution: {integrity: sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=, tarball: http://npm.zhenguanyu.com/toposort/download/toposort-2.0.2.tgz}

  tough-cookie@2.5.0:
    resolution: {integrity: sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=, tarball: http://npm.zhenguanyu.com/tough-cookie/download/tough-cookie-2.5.0.tgz}
    engines: {node: '>=0.8'}

  tryer@1.0.1:
    resolution: {integrity: sha1-8shUBoALmw90yfdGW4HqrSQSUvg=, tarball: http://npm.zhenguanyu.com/tryer/download/tryer-1.0.1.tgz}

  tsconfig-paths@3.15.0:
    resolution: {integrity: sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=, tarball: http://npm.zhenguanyu.com/tsconfig-paths/download/tsconfig-paths-3.15.0.tgz}

  tslib@1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=, tarball: http://npm.zhenguanyu.com/tslib/download/tslib-1.14.1.tgz}

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=, tarball: http://npm.zhenguanyu.com/tslib/download/tslib-2.8.1.tgz}

  tty-browserify@0.0.0:
    resolution: {integrity: sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=, tarball: http://npm.zhenguanyu.com/tty-browserify/download/tty-browserify-0.0.0.tgz}

  tunnel-agent@0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=, tarball: http://npm.zhenguanyu.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz}

  tweetnacl@0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=, tarball: http://npm.zhenguanyu.com/tweetnacl/download/tweetnacl-0.14.5.tgz}

  type-check@0.3.2:
    resolution: {integrity: sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=, tarball: http://npm.zhenguanyu.com/type-check/download/type-check-0.3.2.tgz}
    engines: {node: '>= 0.8.0'}

  type-fest@0.6.0:
    resolution: {integrity: sha1-jSojcNPfiG61yQraHFv2GIrPg4s=, tarball: http://npm.zhenguanyu.com/type-fest/download/type-fest-0.6.0.tgz}
    engines: {node: '>=8'}

  type-is@1.6.18:
    resolution: {integrity: sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=, tarball: http://npm.zhenguanyu.com/type-is/download/type-is-1.6.18.tgz}
    engines: {node: '>= 0.6'}

  typed-array-buffer@1.0.2:
    resolution: {integrity: sha1-GGfF2Dsg/LXM8yZJ5eL8dCRHT/M=, tarball: http://npm.zhenguanyu.com/typed-array-buffer/download/typed-array-buffer-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.1:
    resolution: {integrity: sha1-2Sly08/5mj+i52Wij83A8did7Gc=, tarball: http://npm.zhenguanyu.com/typed-array-byte-length/download/typed-array-byte-length-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.2:
    resolution: {integrity: sha1-+eway5JZ85UJPkVn6zwopYDQIGM=, tarball: http://npm.zhenguanyu.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.6:
    resolution: {integrity: sha1-VxVSB8duZKNFdILf3BydHTxMc6M=, tarball: http://npm.zhenguanyu.com/typed-array-length/download/typed-array-length-1.0.6.tgz}
    engines: {node: '>= 0.4'}

  typedarray@0.0.6:
    resolution: {integrity: sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=, tarball: http://npm.zhenguanyu.com/typedarray/download/typedarray-0.0.6.tgz}

  uglify-js@3.4.10:
    resolution: {integrity: sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=, tarball: http://npm.zhenguanyu.com/uglify-js/download/uglify-js-3.4.10.tgz}
    engines: {node: '>=0.8.0'}
    hasBin: true

  unbox-primitive@1.0.2:
    resolution: {integrity: sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=, tarball: http://npm.zhenguanyu.com/unbox-primitive/download/unbox-primitive-1.0.2.tgz}

  undici-types@6.19.8:
    resolution: {integrity: sha1-NREcnRQ3q4OnzcCrri8m2I7aCgI=, tarball: http://npm.zhenguanyu.com/undici-types/download/undici-types-6.19.8.tgz}

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha1-yzFz/kfKdD4ighbko93EyE1ijMI=, tarball: http://npm.zhenguanyu.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=, tarball: http://npm.zhenguanyu.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=, tarball: http://npm.zhenguanyu.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=, tarball: http://npm.zhenguanyu.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz}
    engines: {node: '>=4'}

  union-value@1.0.1:
    resolution: {integrity: sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=, tarball: http://npm.zhenguanyu.com/union-value/download/union-value-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  uniq@1.0.1:
    resolution: {integrity: sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=, tarball: http://npm.zhenguanyu.com/uniq/download/uniq-1.0.1.tgz}

  uniqs@2.0.0:
    resolution: {integrity: sha1-/+3ks2slKQaW5uFl1KWe25mOawI=, tarball: http://npm.zhenguanyu.com/uniqs/download/uniqs-2.0.0.tgz}

  unique-filename@1.1.1:
    resolution: {integrity: sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=, tarball: http://npm.zhenguanyu.com/unique-filename/download/unique-filename-1.1.1.tgz}

  unique-slug@2.0.2:
    resolution: {integrity: sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=, tarball: http://npm.zhenguanyu.com/unique-slug/download/unique-slug-2.0.2.tgz}

  universalify@0.1.2:
    resolution: {integrity: sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=, tarball: http://npm.zhenguanyu.com/universalify/download/universalify-0.1.2.tgz}
    engines: {node: '>= 4.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=, tarball: http://npm.zhenguanyu.com/unpipe/download/unpipe-1.0.0.tgz}
    engines: {node: '>= 0.8'}

  unquote@1.1.1:
    resolution: {integrity: sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=, tarball: http://npm.zhenguanyu.com/unquote/download/unquote-1.1.1.tgz}

  unset-value@1.0.0:
    resolution: {integrity: sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=, tarball: http://npm.zhenguanyu.com/unset-value/download/unset-value-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  upath@1.2.0:
    resolution: {integrity: sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=, tarball: http://npm.zhenguanyu.com/upath/download/upath-1.2.0.tgz}
    engines: {node: '>=4'}

  update-browserslist-db@1.1.1:
    resolution: {integrity: sha1-gIRvuh156CVH+2YfjRQeCUV1X+U=, tarball: http://npm.zhenguanyu.com/update-browserslist-db/download/update-browserslist-db-1.1.1.tgz}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  upper-case@1.1.3:
    resolution: {integrity: sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=, tarball: http://npm.zhenguanyu.com/upper-case/download/upper-case-1.1.3.tgz}

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=, tarball: http://npm.zhenguanyu.com/uri-js/download/uri-js-4.4.1.tgz}

  urix@0.1.0:
    resolution: {integrity: sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=, tarball: http://npm.zhenguanyu.com/urix/download/urix-0.1.0.tgz}
    deprecated: Please see https://github.com/lydell/urix#deprecated

  url-loader@1.1.2:
    resolution: {integrity: sha1-uXHRkbg69pPF4/6kBkvp4fLX+Ng=, tarball: http://npm.zhenguanyu.com/url-loader/download/url-loader-1.1.2.tgz}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^3.0.0 || ^4.0.0

  url-parse@1.5.10:
    resolution: {integrity: sha1-nTwvc2wddd070r5QfcwRHx4uqcE=, tarball: http://npm.zhenguanyu.com/url-parse/download/url-parse-1.5.10.tgz}

  url@0.11.4:
    resolution: {integrity: sha1-rcp3s1YtVrcnRudrMwt/J7ZyHzw=, tarball: http://npm.zhenguanyu.com/url/download/url-0.11.4.tgz}
    engines: {node: '>= 0.4'}

  use@3.1.1:
    resolution: {integrity: sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=, tarball: http://npm.zhenguanyu.com/use/download/use-3.1.1.tgz}
    engines: {node: '>=0.10.0'}

  util-deprecate@1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=, tarball: http://npm.zhenguanyu.com/util-deprecate/download/util-deprecate-1.0.2.tgz}

  util.promisify@1.0.0:
    resolution: {integrity: sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=, tarball: http://npm.zhenguanyu.com/util.promisify/download/util.promisify-1.0.0.tgz}

  util.promisify@1.0.1:
    resolution: {integrity: sha1-a693dLgO6w91INi4HQeYKlmruu4=, tarball: http://npm.zhenguanyu.com/util.promisify/download/util.promisify-1.0.1.tgz}

  util@0.10.4:
    resolution: {integrity: sha1-OqASW/5mikZy3liFfTrOJ+y3aQE=, tarball: http://npm.zhenguanyu.com/util/download/util-0.10.4.tgz}

  util@0.11.1:
    resolution: {integrity: sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=, tarball: http://npm.zhenguanyu.com/util/download/util-0.11.1.tgz}

  utila@0.4.0:
    resolution: {integrity: sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=, tarball: http://npm.zhenguanyu.com/utila/download/utila-0.4.0.tgz}

  utils-merge@1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=, tarball: http://npm.zhenguanyu.com/utils-merge/download/utils-merge-1.0.1.tgz}
    engines: {node: '>= 0.4.0'}

  uuid@3.4.0:
    resolution: {integrity: sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=, tarball: http://npm.zhenguanyu.com/uuid/download/uuid-3.4.0.tgz}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  uuid@8.3.2:
    resolution: {integrity: sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=, tarball: http://npm.zhenguanyu.com/uuid/download/uuid-8.3.2.tgz}
    hasBin: true

  validate-npm-package-license@3.0.4:
    resolution: {integrity: sha1-/JH2uce6FchX9MssXe/uw51PQQo=, tarball: http://npm.zhenguanyu.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz}

  vary@1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=, tarball: http://npm.zhenguanyu.com/vary/download/vary-1.1.2.tgz}
    engines: {node: '>= 0.8'}

  vendors@1.0.4:
    resolution: {integrity: sha1-4rgApT56Kbk1BsPPQRANFsTErY4=, tarball: http://npm.zhenguanyu.com/vendors/download/vendors-1.0.4.tgz}

  verror@1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=, tarball: http://npm.zhenguanyu.com/verror/download/verror-1.10.0.tgz}
    engines: {'0': node >=0.6.0}

  vm-browserify@1.1.2:
    resolution: {integrity: sha1-eGQcSIuObKkadfUR56OzKobl3aA=, tarball: http://npm.zhenguanyu.com/vm-browserify/download/vm-browserify-1.1.2.tgz}

  vue-awesome-swiper@3.1.3:
    resolution: {integrity: sha1-BVALUB/7P+yb9+uZhbz0roNg7Z4=, tarball: http://npm.zhenguanyu.com/vue-awesome-swiper/download/vue-awesome-swiper-3.1.3.tgz}
    engines: {node: '>= 4.0.0', npm: '>= 3.0.0'}

  vue-eslint-parser@2.0.3:
    resolution: {integrity: sha1-wmjJbG2Uz+PZOKX3WTlZsMozYNE=, tarball: http://npm.zhenguanyu.com/vue-eslint-parser/download/vue-eslint-parser-2.0.3.tgz}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: '>=3.9.0'

  vue-eslint-parser@5.0.0:
    resolution: {integrity: sha1-APTk2pTsl0uCGib/DtD3p4QCuKE=, tarball: http://npm.zhenguanyu.com/vue-eslint-parser/download/vue-eslint-parser-5.0.0.tgz}
    engines: {node: '>=6.5'}
    peerDependencies:
      eslint: ^5.0.0

  vue-hot-reload-api@2.3.4:
    resolution: {integrity: sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=, tarball: http://npm.zhenguanyu.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz}

  vue-loader@15.11.1:
    resolution: {integrity: sha1-3ukRaSESdu1DxXFcrviKVrH0l7A=, tarball: http://npm.zhenguanyu.com/vue-loader/download/vue-loader-15.11.1.tgz}
    peerDependencies:
      '@vue/compiler-sfc': ^3.0.8
      cache-loader: '*'
      css-loader: '*'
      prettier: '*'
      vue-template-compiler: '*'
      webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      cache-loader:
        optional: true
      prettier:
        optional: true
      vue-template-compiler:
        optional: true

  vue-router@3.0.6:
    resolution: {integrity: sha1-Lk8PnLsLltAgWrJpDP5YiTUTasM=, tarball: http://npm.zhenguanyu.com/vue-router/download/vue-router-3.0.6.tgz}
    peerDependencies:
      vue: ^2

  vue-style-loader@4.1.3:
    resolution: {integrity: sha1-bVWGOlH6dXqyTonZNxRlByqnvDU=, tarball: http://npm.zhenguanyu.com/vue-style-loader/download/vue-style-loader-4.1.3.tgz}

  vue-template-compiler@2.7.16:
    resolution: {integrity: sha1-yBstR3UyZMd6wDuZZqRmN0grsDs=, tarball: http://npm.zhenguanyu.com/vue-template-compiler/download/vue-template-compiler-2.7.16.tgz}

  vue-template-es2015-compiler@1.9.1:
    resolution: {integrity: sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=, tarball: http://npm.zhenguanyu.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz}

  vue@2.7.16:
    resolution: {integrity: sha1-mMYN6d75nA49qNrlmzBOrUO5Z8k=, tarball: http://npm.zhenguanyu.com/vue/download/vue-2.7.16.tgz}
    deprecated: Vue 2 has reached EOL and is no longer actively maintained. See https://v2.vuejs.org/eol/ for more details.

  watchpack-chokidar2@2.0.1:
    resolution: {integrity: sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=, tarball: http://npm.zhenguanyu.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz}

  watchpack@1.7.5:
    resolution: {integrity: sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=, tarball: http://npm.zhenguanyu.com/watchpack/download/watchpack-1.7.5.tgz}

  wbuf@1.7.3:
    resolution: {integrity: sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=, tarball: http://npm.zhenguanyu.com/wbuf/download/wbuf-1.7.3.tgz}

  wcwidth@1.0.1:
    resolution: {integrity: sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=, tarball: http://npm.zhenguanyu.com/wcwidth/download/wcwidth-1.0.1.tgz}

  webpack-bundle-analyzer@3.9.0:
    resolution: {integrity: sha1-9vlNsQj7V05BWtMT3kGicH0z7zw=, tarball: http://npm.zhenguanyu.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.9.0.tgz}
    engines: {node: '>= 6.14.4'}
    hasBin: true

  webpack-chain@4.12.1:
    resolution: {integrity: sha1-bIQ5u7KrVQlS1g4eqTGRQZBsAqY=, tarball: http://npm.zhenguanyu.com/webpack-chain/download/webpack-chain-4.12.1.tgz}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.

  webpack-dev-middleware@3.7.3:
    resolution: {integrity: sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU=, tarball: http://npm.zhenguanyu.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.3.tgz}
    engines: {node: '>= 6'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  webpack-dev-server@3.11.3:
    resolution: {integrity: sha1-jIa50oEr8TXTybzm8HtxjjD3w9M=, tarball: http://npm.zhenguanyu.com/webpack-dev-server/download/webpack-dev-server-3.11.3.tgz}
    engines: {node: '>= 6.11.5'}
    hasBin: true
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  webpack-log@2.0.0:
    resolution: {integrity: sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=, tarball: http://npm.zhenguanyu.com/webpack-log/download/webpack-log-2.0.0.tgz}
    engines: {node: '>= 6'}

  webpack-merge@4.2.2:
    resolution: {integrity: sha1-onxS6ng9E5iv0gh/VH17nS9DY00=, tarball: http://npm.zhenguanyu.com/webpack-merge/download/webpack-merge-4.2.2.tgz}

  webpack-sources@1.4.3:
    resolution: {integrity: sha1-7t2OwLko+/HL/plOItLYkPMwqTM=, tarball: http://npm.zhenguanyu.com/webpack-sources/download/webpack-sources-1.4.3.tgz}

  webpack@4.28.4:
    resolution: {integrity: sha1-HdrmyJiH1++3Uq3ww80yubB+rNA=, tarball: http://npm.zhenguanyu.com/webpack/download/webpack-4.28.4.tgz}
    engines: {node: '>=6.11.5'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
      webpack-command: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
      webpack-command:
        optional: true

  webpack@4.47.0:
    resolution: {integrity: sha1-i4oCFS1wdq6wO2G0fa0u7tmBDrw=, tarball: http://npm.zhenguanyu.com/webpack/download/webpack-4.47.0.tgz}
    engines: {node: '>=6.11.5'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
      webpack-command: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
      webpack-command:
        optional: true

  websocket-driver@0.7.4:
    resolution: {integrity: sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=, tarball: http://npm.zhenguanyu.com/websocket-driver/download/websocket-driver-0.7.4.tgz}
    engines: {node: '>=0.8.0'}

  websocket-extensions@0.1.4:
    resolution: {integrity: sha1-f4RzvIOd/YdgituV1+sHUhFXikI=, tarball: http://npm.zhenguanyu.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz}
    engines: {node: '>=0.8.0'}

  which-boxed-primitive@1.0.2:
    resolution: {integrity: sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=, tarball: http://npm.zhenguanyu.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz}

  which-module@2.0.1:
    resolution: {integrity: sha1-d2sf412Qrr6Z6KwV6yQJM4mkpAk=, tarball: http://npm.zhenguanyu.com/which-module/download/which-module-2.0.1.tgz}

  which-typed-array@1.1.15:
    resolution: {integrity: sha1-JkhZ6bEaZJs4i/qvT3Z98fd5s40=, tarball: http://npm.zhenguanyu.com/which-typed-array/download/which-typed-array-1.1.15.tgz}
    engines: {node: '>= 0.4'}

  which@1.3.1:
    resolution: {integrity: sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=, tarball: http://npm.zhenguanyu.com/which/download/which-1.3.1.tgz}
    hasBin: true

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=, tarball: http://npm.zhenguanyu.com/which/download/which-2.0.2.tgz}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=, tarball: http://npm.zhenguanyu.com/word-wrap/download/word-wrap-1.2.5.tgz}
    engines: {node: '>=0.10.0'}

  worker-farm@1.7.0:
    resolution: {integrity: sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=, tarball: http://npm.zhenguanyu.com/worker-farm/download/worker-farm-1.7.0.tgz}

  wrap-ansi@3.0.1:
    resolution: {integrity: sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=, tarball: http://npm.zhenguanyu.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz}
    engines: {node: '>=4'}

  wrap-ansi@5.1.0:
    resolution: {integrity: sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=, tarball: http://npm.zhenguanyu.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz}
    engines: {node: '>=6'}

  wrap-ansi@6.2.0:
    resolution: {integrity: sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=, tarball: http://npm.zhenguanyu.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz}
    engines: {node: '>=8'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=, tarball: http://npm.zhenguanyu.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz}
    engines: {node: '>=10'}

  wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=, tarball: http://npm.zhenguanyu.com/wrappy/download/wrappy-1.0.2.tgz}

  write@0.2.1:
    resolution: {integrity: sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=, tarball: http://npm.zhenguanyu.com/write/download/write-0.2.1.tgz}
    engines: {node: '>=0.10.0'}

  write@1.0.3:
    resolution: {integrity: sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=, tarball: http://npm.zhenguanyu.com/write/download/write-1.0.3.tgz}
    engines: {node: '>=4'}

  ws@6.2.3:
    resolution: {integrity: sha1-zMluSt1f1v7bxJGQMHXIXFoR2e4=, tarball: http://npm.zhenguanyu.com/ws/download/ws-6.2.3.tgz}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xtend@4.0.2:
    resolution: {integrity: sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=, tarball: http://npm.zhenguanyu.com/xtend/download/xtend-4.0.2.tgz}
    engines: {node: '>=0.4'}

  y18n@4.0.3:
    resolution: {integrity: sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=, tarball: http://npm.zhenguanyu.com/y18n/download/y18n-4.0.3.tgz}

  y18n@5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=, tarball: http://npm.zhenguanyu.com/y18n/download/y18n-5.0.8.tgz}
    engines: {node: '>=10'}

  yallist@2.1.2:
    resolution: {integrity: sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=, tarball: http://npm.zhenguanyu.com/yallist/download/yallist-2.1.2.tgz}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=, tarball: http://npm.zhenguanyu.com/yallist/download/yallist-3.1.1.tgz}

  yargs-parser@13.1.2:
    resolution: {integrity: sha1-Ew8JcC667vJlDVTObj5XBvek+zg=, tarball: http://npm.zhenguanyu.com/yargs-parser/download/yargs-parser-13.1.2.tgz}

  yargs-parser@18.1.3:
    resolution: {integrity: sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=, tarball: http://npm.zhenguanyu.com/yargs-parser/download/yargs-parser-18.1.3.tgz}
    engines: {node: '>=6'}

  yargs-parser@20.2.9:
    resolution: {integrity: sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=, tarball: http://npm.zhenguanyu.com/yargs-parser/download/yargs-parser-20.2.9.tgz}
    engines: {node: '>=10'}

  yargs@13.3.2:
    resolution: {integrity: sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=, tarball: http://npm.zhenguanyu.com/yargs/download/yargs-13.3.2.tgz}

  yargs@15.4.1:
    resolution: {integrity: sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=, tarball: http://npm.zhenguanyu.com/yargs/download/yargs-15.4.1.tgz}
    engines: {node: '>=8'}

  yargs@16.2.0:
    resolution: {integrity: sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=, tarball: http://npm.zhenguanyu.com/yargs/download/yargs-16.2.0.tgz}
    engines: {node: '>=10'}

  yup@0.27.0:
    resolution: {integrity: sha1-+MsZjI590hJL7dwkV1cTKQlrBuc=, tarball: http://npm.zhenguanyu.com/yup/download/yup-0.27.0.tgz}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.2': {}

  '@babel/core@7.26.0':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.2
      '@babel/helper-compilation-targets': 7.25.9
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.0)
      '@babel/helpers': 7.26.0
      '@babel/parser': 7.26.2
      '@babel/template': 7.25.9
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
      convert-source-map: 2.0.0
      debug: 4.3.7(supports-color@6.1.0)
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.26.2':
    dependencies:
      '@babel/parser': 7.26.2
      '@babel/types': 7.26.0
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.0.2

  '@babel/helper-annotate-as-pure@7.25.9':
    dependencies:
      '@babel/types': 7.26.0

  '@babel/helper-builder-binary-assignment-operator-visitor@7.25.9':
    dependencies:
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-compilation-targets@7.25.9':
    dependencies:
      '@babel/compat-data': 7.26.2
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.2
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/helper-replace-supers': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/traverse': 7.25.9
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-create-regexp-features-plugin@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-annotate-as-pure': 7.25.9
      regexpu-core: 6.1.1
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.2(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-compilation-targets': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      debug: 4.3.7(supports-color@6.1.0)
      lodash.debounce: 4.0.8
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-environment-visitor@7.24.7':
    dependencies:
      '@babel/types': 7.26.0

  '@babel/helper-member-expression-to-functions@7.25.9':
    dependencies:
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.25.9':
    dependencies:
      '@babel/types': 7.26.0

  '@babel/helper-plugin-utils@7.25.9': {}

  '@babel/helper-remap-async-to-generator@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-wrap-function': 7.25.9
      '@babel/traverse': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-replace-supers@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/traverse': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-simple-access@7.25.9':
    dependencies:
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    dependencies:
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helper-wrap-function@7.25.9':
    dependencies:
      '@babel/template': 7.25.9
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helpers@7.26.0':
    dependencies:
      '@babel/template': 7.25.9
      '@babel/types': 7.26.0

  '@babel/parser@7.26.2':
    dependencies:
      '@babel/types': 7.26.0

  '@babel/plugin-proposal-async-generator-functions@7.20.7(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-environment-visitor': 7.24.7
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-remap-async-to-generator': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.26.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-create-class-features-plugin': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-decorators@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-create-class-features-plugin': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/plugin-syntax-decorators': 7.25.9(@babel/core@7.26.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-json-strings@7.18.6(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.26.0)

  '@babel/plugin-proposal-object-rest-spread@7.20.7(@babel/core@7.26.0)':
    dependencies:
      '@babel/compat-data': 7.26.2
      '@babel/core': 7.26.0
      '@babel/helper-compilation-targets': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.26.0)
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.0)

  '@babel/plugin-proposal-optional-catch-binding@7.18.6(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.26.0)

  '@babel/plugin-proposal-unicode-property-regex@7.18.6(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-create-regexp-features-plugin': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-decorators@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-async-to-generator@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-remap-async-to-generator': 7.25.9(@babel/core@7.26.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-block-scoped-functions@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-block-scoping@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-classes@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-compilation-targets': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-replace-supers': 7.25.9(@babel/core@7.26.0)
      '@babel/traverse': 7.25.9
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/template': 7.25.9

  '@babel/plugin-transform-destructuring@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-dotall-regex@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-create-regexp-features-plugin': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-duplicate-keys@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-exponentiation-operator@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-for-of@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-function-name@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-compilation-targets': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/traverse': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-literals@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-modules-amd@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-commonjs@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-simple-access': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-systemjs@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-umd@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-create-regexp-features-plugin': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-new-target@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-object-super@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-replace-supers': 7.25.9(@babel/core@7.26.0)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-regenerator@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9
      regenerator-transform: 0.15.2

  '@babel/plugin-transform-runtime@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      babel-plugin-polyfill-corejs2: 0.4.11(@babel/core@7.26.0)
      babel-plugin-polyfill-corejs3: 0.10.6(@babel/core@7.26.0)
      babel-plugin-polyfill-regenerator: 0.6.2(@babel/core@7.26.0)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-shorthand-properties@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-spread@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-sticky-regex@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-template-literals@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-typeof-symbol@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-unicode-regex@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-create-regexp-features-plugin': 7.25.9(@babel/core@7.26.0)
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/preset-env@7.3.4(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/plugin-proposal-async-generator-functions': 7.20.7(@babel/core@7.26.0)
      '@babel/plugin-proposal-json-strings': 7.18.6(@babel/core@7.26.0)
      '@babel/plugin-proposal-object-rest-spread': 7.20.7(@babel/core@7.26.0)
      '@babel/plugin-proposal-optional-catch-binding': 7.18.6(@babel/core@7.26.0)
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6(@babel/core@7.26.0)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.26.0)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.26.0)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.26.0)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.26.0)
      '@babel/plugin-transform-arrow-functions': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-async-to-generator': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-block-scoped-functions': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-block-scoping': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-classes': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-computed-properties': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-destructuring': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-dotall-regex': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-duplicate-keys': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-exponentiation-operator': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-for-of': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-function-name': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-literals': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-modules-amd': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-modules-commonjs': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-modules-systemjs': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-modules-umd': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-new-target': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-object-super': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-regenerator': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-shorthand-properties': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-spread': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-sticky-regex': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-template-literals': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-typeof-symbol': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-unicode-regex': 7.25.9(@babel/core@7.26.0)
      browserslist: 4.24.2
      invariant: 2.2.4
      js-levenshtein: 1.1.6
      semver: 5.7.2
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime-corejs2@7.26.0':
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.14.1

  '@babel/runtime@7.26.0':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.25.9':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.2
      '@babel/types': 7.26.0

  '@babel/traverse@7.25.9':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.2
      '@babel/parser': 7.26.2
      '@babel/template': 7.25.9
      '@babel/types': 7.26.0
      debug: 4.3.7(supports-color@6.1.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.26.0':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@hapi/address@2.1.4': {}

  '@hapi/bourne@1.3.2': {}

  '@hapi/hoek@8.5.1': {}

  '@hapi/joi@15.1.1':
    dependencies:
      '@hapi/address': 2.1.4
      '@hapi/bourne': 1.3.2
      '@hapi/hoek': 8.5.1
      '@hapi/topo': 3.1.6

  '@hapi/topo@3.1.6':
    dependencies:
      '@hapi/hoek': 8.5.1

  '@intervolga/optimize-cssnano-plugin@1.0.6(webpack@4.47.0)':
    dependencies:
      cssnano: 4.1.11
      cssnano-preset-default: 4.0.8
      postcss: 7.0.39
      webpack: 4.47.0

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@mrmlnc/readdir-enhanced@2.2.1':
    dependencies:
      call-me-maybe: 1.0.2
      glob-to-regexp: 0.3.0

  '@nodelib/fs.stat@1.1.3': {}

  '@rtsao/scc@1.1.0': {}

  '@samverschueren/stream-to-observable@0.3.1(rxjs@6.6.7)':
    dependencies:
      any-observable: 0.3.0(rxjs@6.6.7)
    optionalDependencies:
      rxjs: 6.6.7
    transitivePeerDependencies:
      - zenObservable

  '@soda/friendly-errors-webpack-plugin@1.8.1(webpack@4.47.0)':
    dependencies:
      chalk: 3.0.0
      error-stack-parser: 2.1.4
      string-width: 4.2.3
      strip-ansi: 6.0.1
      webpack: 4.47.0

  '@tutor/ng-cli@9.0.47':
    dependencies:
      axios: 0.19.2
      chalk: 4.1.2
      glob: 7.2.3
      tslib: 2.8.1
      yargs: 15.4.1
    transitivePeerDependencies:
      - supports-color

  '@types/glob@7.2.0':
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 22.8.6

  '@types/json-schema@7.0.15': {}

  '@types/json5@0.0.29': {}

  '@types/minimatch@5.1.2': {}

  '@types/node@22.8.6':
    dependencies:
      undici-types: 6.19.8

  '@types/normalize-package-data@2.4.4': {}

  '@types/q@1.5.8': {}

  '@vue/babel-helper-vue-jsx-merge-props@1.4.0': {}

  '@vue/babel-plugin-transform-vue-jsx@1.4.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      html-tags: 2.0.0
      lodash.kebabcase: 4.1.1
      svg-tags: 1.0.0
    transitivePeerDependencies:
      - supports-color

  '@vue/babel-preset-app@3.12.1(@babel/core@7.26.0)(vue@2.7.16)':
    dependencies:
      '@babel/helper-module-imports': 7.25.9
      '@babel/plugin-proposal-class-properties': 7.18.6(@babel/core@7.26.0)
      '@babel/plugin-proposal-decorators': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.26.0)
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-runtime': 7.25.9(@babel/core@7.26.0)
      '@babel/preset-env': 7.3.4(@babel/core@7.26.0)
      '@babel/runtime': 7.26.0
      '@babel/runtime-corejs2': 7.26.0
      '@vue/babel-preset-jsx': 1.4.0(@babel/core@7.26.0)(vue@2.7.16)
      babel-plugin-dynamic-import-node: 2.3.3
      babel-plugin-module-resolver: 3.2.0
      core-js: 2.6.12
    transitivePeerDependencies:
      - '@babel/core'
      - supports-color
      - vue

  '@vue/babel-preset-jsx@1.4.0(@babel/core@7.26.0)(vue@2.7.16)':
    dependencies:
      '@babel/core': 7.26.0
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0(@babel/core@7.26.0)
      '@vue/babel-sugar-composition-api-inject-h': 1.4.0(@babel/core@7.26.0)
      '@vue/babel-sugar-composition-api-render-instance': 1.4.0(@babel/core@7.26.0)
      '@vue/babel-sugar-functional-vue': 1.4.0(@babel/core@7.26.0)
      '@vue/babel-sugar-inject-h': 1.4.0(@babel/core@7.26.0)
      '@vue/babel-sugar-v-model': 1.4.0(@babel/core@7.26.0)
      '@vue/babel-sugar-v-on': 1.4.0(@babel/core@7.26.0)
    optionalDependencies:
      vue: 2.7.16
    transitivePeerDependencies:
      - supports-color

  '@vue/babel-sugar-composition-api-inject-h@1.4.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)

  '@vue/babel-sugar-composition-api-render-instance@1.4.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)

  '@vue/babel-sugar-functional-vue@1.4.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)

  '@vue/babel-sugar-inject-h@1.4.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)

  '@vue/babel-sugar-v-model@1.4.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0(@babel/core@7.26.0)
      camelcase: 5.3.1
      html-tags: 2.0.0
      svg-tags: 1.0.0
    transitivePeerDependencies:
      - supports-color

  '@vue/babel-sugar-v-on@1.4.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0(@babel/core@7.26.0)
      camelcase: 5.3.1
    transitivePeerDependencies:
      - supports-color

  '@vue/cli-overlay@3.12.1': {}

  '@vue/cli-plugin-babel@3.12.1(vue@2.7.16)':
    dependencies:
      '@babel/core': 7.26.0
      '@vue/babel-preset-app': 3.12.1(@babel/core@7.26.0)(vue@2.7.16)
      '@vue/cli-shared-utils': 3.12.1
      babel-loader: 8.4.1(@babel/core@7.26.0)(webpack@4.47.0)
      webpack: 4.47.0
    transitivePeerDependencies:
      - supports-color
      - vue
      - webpack-cli
      - webpack-command

  '@vue/cli-plugin-eslint@3.7.0':
    dependencies:
      '@vue/cli-shared-utils': 3.12.1
      babel-eslint: 10.1.0(eslint@4.19.1)
      eslint-loader: 2.2.1(eslint@4.19.1)(webpack@4.28.4)
      globby: 9.2.0
      webpack: 4.28.4
    optionalDependencies:
      eslint: 4.19.1
      eslint-plugin-vue: 4.7.1(eslint@4.19.1)
    transitivePeerDependencies:
      - supports-color
      - webpack-cli
      - webpack-command

  '@vue/cli-service@3.12.1(lodash@4.17.21)(prettier@1.19.1)(vue-template-compiler@2.7.16)':
    dependencies:
      '@intervolga/optimize-cssnano-plugin': 1.0.6(webpack@4.47.0)
      '@soda/friendly-errors-webpack-plugin': 1.8.1(webpack@4.47.0)
      '@vue/cli-overlay': 3.12.1
      '@vue/cli-shared-utils': 3.12.1
      '@vue/component-compiler-utils': 3.3.0(lodash@4.17.21)
      '@vue/preload-webpack-plugin': 1.1.2(html-webpack-plugin@3.2.0(webpack@4.47.0))(webpack@4.47.0)
      '@vue/web-component-wrapper': 1.3.0
      acorn: 6.4.2
      acorn-walk: 6.2.0
      address: 1.2.2
      autoprefixer: 9.8.8
      browserslist: 4.24.2
      cache-loader: 2.0.1(webpack@4.47.0)
      case-sensitive-paths-webpack-plugin: 2.4.0
      chalk: 2.4.2
      cli-highlight: 2.1.11
      clipboardy: 2.3.0
      cliui: 5.0.0
      copy-webpack-plugin: 4.6.0
      css-loader: 1.0.1(webpack@4.47.0)
      cssnano: 4.1.11
      current-script-polyfill: 1.0.0
      debug: 4.3.7(supports-color@6.1.0)
      default-gateway: 5.0.5
      dotenv: 7.0.0
      dotenv-expand: 5.1.0
      escape-string-regexp: 1.0.5
      file-loader: 3.0.1(webpack@4.47.0)
      fs-extra: 7.0.1
      globby: 9.2.0
      hash-sum: 1.0.2
      html-webpack-plugin: 3.2.0(webpack@4.47.0)
      launch-editor-middleware: 2.9.1
      lodash.defaultsdeep: 4.6.1
      lodash.mapvalues: 4.6.0
      lodash.transform: 4.6.0
      mini-css-extract-plugin: 0.8.2(webpack@4.47.0)
      minimist: 1.2.8
      ora: 3.4.0
      portfinder: 1.0.32(supports-color@6.1.0)
      postcss-loader: 3.0.0
      read-pkg: 5.2.0
      semver: 6.3.1
      slash: 2.0.0
      source-map-url: 0.4.1
      ssri: 6.0.2
      string.prototype.padend: 3.1.6
      terser-webpack-plugin: 1.4.6(webpack@4.47.0)
      thread-loader: 2.1.3(webpack@4.47.0)
      url-loader: 1.1.2(webpack@4.47.0)
      vue-loader: 15.11.1(cache-loader@2.0.1(webpack@4.47.0))(css-loader@1.0.1(webpack@4.47.0))(lodash@4.17.21)(prettier@1.19.1)(vue-template-compiler@2.7.16)(webpack@4.47.0)
      vue-template-compiler: 2.7.16
      webpack: 4.47.0
      webpack-bundle-analyzer: 3.9.0
      webpack-chain: 4.12.1
      webpack-dev-server: 3.11.3(webpack@4.47.0)
      webpack-merge: 4.2.2
    transitivePeerDependencies:
      - '@vue/compiler-sfc'
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - bufferutil
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - prettier
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - supports-color
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - utf-8-validate
      - vash
      - velocityjs
      - walrus
      - webpack-cli
      - webpack-command
      - whiskers

  '@vue/cli-shared-utils@3.12.1':
    dependencies:
      '@hapi/joi': 15.1.1
      chalk: 2.4.2
      execa: 1.0.0
      launch-editor: 2.9.1
      lru-cache: 5.1.1
      node-ipc: 9.2.1
      open: 6.4.0
      ora: 3.4.0
      request: 2.88.2
      request-promise-native: 1.0.9(request@2.88.2)
      semver: 6.3.1
      string.prototype.padstart: 3.1.6

  '@vue/compiler-sfc@2.7.16':
    dependencies:
      '@babel/parser': 7.26.2
      postcss: 8.4.47
      source-map: 0.6.1
    optionalDependencies:
      prettier: 2.8.8

  '@vue/component-compiler-utils@3.3.0(lodash@4.17.21)':
    dependencies:
      consolidate: 0.15.1(lodash@4.17.21)
      hash-sum: 1.0.2
      lru-cache: 4.1.5
      merge-source-map: 1.1.0
      postcss: 7.0.39
      postcss-selector-parser: 6.1.2
      source-map: 0.6.1
      vue-template-es2015-compiler: 1.9.1
    optionalDependencies:
      prettier: 2.8.8
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers

  '@vue/eslint-config-prettier@4.0.1(eslint@5.16.0)':
    dependencies:
      eslint-config-prettier: 3.6.0(eslint@5.16.0)
      eslint-plugin-prettier: 3.4.1(eslint-config-prettier@3.6.0(eslint@5.16.0))(eslint@5.16.0)(prettier@1.19.1)
      prettier: 1.19.1
    transitivePeerDependencies:
      - eslint

  '@vue/eslint-config-standard@4.0.0(eslint@5.16.0)':
    dependencies:
      eslint-config-standard: 12.0.0(eslint-plugin-import@2.31.0(eslint@5.16.0))(eslint-plugin-node@8.0.1(eslint@5.16.0))(eslint-plugin-promise@4.3.1)(eslint-plugin-standard@4.1.0(eslint@5.16.0))(eslint@5.16.0)
      eslint-plugin-import: 2.31.0(eslint@5.16.0)
      eslint-plugin-node: 8.0.1(eslint@5.16.0)
      eslint-plugin-promise: 4.3.1
      eslint-plugin-standard: 4.1.0(eslint@5.16.0)
    transitivePeerDependencies:
      - '@typescript-eslint/parser'
      - eslint
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  '@vue/preload-webpack-plugin@1.1.2(html-webpack-plugin@3.2.0(webpack@4.47.0))(webpack@4.47.0)':
    dependencies:
      html-webpack-plugin: 3.2.0(webpack@4.47.0)
      webpack: 4.47.0

  '@vue/web-component-wrapper@1.3.0': {}

  '@webassemblyjs/ast@1.7.11':
    dependencies:
      '@webassemblyjs/helper-module-context': 1.7.11
      '@webassemblyjs/helper-wasm-bytecode': 1.7.11
      '@webassemblyjs/wast-parser': 1.7.11

  '@webassemblyjs/ast@1.9.0':
    dependencies:
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0

  '@webassemblyjs/floating-point-hex-parser@1.7.11': {}

  '@webassemblyjs/floating-point-hex-parser@1.9.0': {}

  '@webassemblyjs/helper-api-error@1.7.11': {}

  '@webassemblyjs/helper-api-error@1.9.0': {}

  '@webassemblyjs/helper-buffer@1.7.11': {}

  '@webassemblyjs/helper-buffer@1.9.0': {}

  '@webassemblyjs/helper-code-frame@1.7.11':
    dependencies:
      '@webassemblyjs/wast-printer': 1.7.11

  '@webassemblyjs/helper-code-frame@1.9.0':
    dependencies:
      '@webassemblyjs/wast-printer': 1.9.0

  '@webassemblyjs/helper-fsm@1.7.11': {}

  '@webassemblyjs/helper-fsm@1.9.0': {}

  '@webassemblyjs/helper-module-context@1.7.11': {}

  '@webassemblyjs/helper-module-context@1.9.0':
    dependencies:
      '@webassemblyjs/ast': 1.9.0

  '@webassemblyjs/helper-wasm-bytecode@1.7.11': {}

  '@webassemblyjs/helper-wasm-bytecode@1.9.0': {}

  '@webassemblyjs/helper-wasm-section@1.7.11':
    dependencies:
      '@webassemblyjs/ast': 1.7.11
      '@webassemblyjs/helper-buffer': 1.7.11
      '@webassemblyjs/helper-wasm-bytecode': 1.7.11
      '@webassemblyjs/wasm-gen': 1.7.11

  '@webassemblyjs/helper-wasm-section@1.9.0':
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0

  '@webassemblyjs/ieee754@1.7.11':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/ieee754@1.9.0':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.7.11':
    dependencies:
      '@xtuc/long': 4.2.1

  '@webassemblyjs/leb128@1.9.0':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.7.11': {}

  '@webassemblyjs/utf8@1.9.0': {}

  '@webassemblyjs/wasm-edit@1.7.11':
    dependencies:
      '@webassemblyjs/ast': 1.7.11
      '@webassemblyjs/helper-buffer': 1.7.11
      '@webassemblyjs/helper-wasm-bytecode': 1.7.11
      '@webassemblyjs/helper-wasm-section': 1.7.11
      '@webassemblyjs/wasm-gen': 1.7.11
      '@webassemblyjs/wasm-opt': 1.7.11
      '@webassemblyjs/wasm-parser': 1.7.11
      '@webassemblyjs/wast-printer': 1.7.11

  '@webassemblyjs/wasm-edit@1.9.0':
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/helper-wasm-section': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-opt': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      '@webassemblyjs/wast-printer': 1.9.0

  '@webassemblyjs/wasm-gen@1.7.11':
    dependencies:
      '@webassemblyjs/ast': 1.7.11
      '@webassemblyjs/helper-wasm-bytecode': 1.7.11
      '@webassemblyjs/ieee754': 1.7.11
      '@webassemblyjs/leb128': 1.7.11
      '@webassemblyjs/utf8': 1.7.11

  '@webassemblyjs/wasm-gen@1.9.0':
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0

  '@webassemblyjs/wasm-opt@1.7.11':
    dependencies:
      '@webassemblyjs/ast': 1.7.11
      '@webassemblyjs/helper-buffer': 1.7.11
      '@webassemblyjs/wasm-gen': 1.7.11
      '@webassemblyjs/wasm-parser': 1.7.11

  '@webassemblyjs/wasm-opt@1.9.0':
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0

  '@webassemblyjs/wasm-parser@1.7.11':
    dependencies:
      '@webassemblyjs/ast': 1.7.11
      '@webassemblyjs/helper-api-error': 1.7.11
      '@webassemblyjs/helper-wasm-bytecode': 1.7.11
      '@webassemblyjs/ieee754': 1.7.11
      '@webassemblyjs/leb128': 1.7.11
      '@webassemblyjs/utf8': 1.7.11

  '@webassemblyjs/wasm-parser@1.9.0':
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0

  '@webassemblyjs/wast-parser@1.7.11':
    dependencies:
      '@webassemblyjs/ast': 1.7.11
      '@webassemblyjs/floating-point-hex-parser': 1.7.11
      '@webassemblyjs/helper-api-error': 1.7.11
      '@webassemblyjs/helper-code-frame': 1.7.11
      '@webassemblyjs/helper-fsm': 1.7.11
      '@xtuc/long': 4.2.1

  '@webassemblyjs/wast-parser@1.9.0':
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/floating-point-hex-parser': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-code-frame': 1.9.0
      '@webassemblyjs/helper-fsm': 1.9.0
      '@xtuc/long': 4.2.2

  '@webassemblyjs/wast-printer@1.7.11':
    dependencies:
      '@webassemblyjs/ast': 1.7.11
      '@webassemblyjs/wast-parser': 1.7.11
      '@xtuc/long': 4.2.1

  '@webassemblyjs/wast-printer@1.9.0':
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
      '@xtuc/long': 4.2.2

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.1': {}

  '@xtuc/long@4.2.2': {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-dynamic-import@3.0.0:
    dependencies:
      acorn: 5.7.4

  acorn-jsx@3.0.1:
    dependencies:
      acorn: 3.3.0

  acorn-jsx@5.3.2(acorn@6.4.2):
    dependencies:
      acorn: 6.4.2

  acorn-walk@6.2.0: {}

  acorn-walk@7.2.0: {}

  acorn@3.3.0: {}

  acorn@5.7.4: {}

  acorn@6.4.2: {}

  acorn@7.4.1: {}

  acorn@8.14.0: {}

  address@1.2.2: {}

  ajv-errors@1.0.1(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv-keywords@2.1.1(ajv@5.5.2):
    dependencies:
      ajv: 5.5.2

  ajv-keywords@3.5.2(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv@5.5.2:
    dependencies:
      co: 4.6.0
      fast-deep-equal: 1.1.0
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.3.1

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  alphanum-sort@1.0.2: {}

  ansi-colors@3.2.4: {}

  ansi-escapes@3.2.0: {}

  ansi-html-community@0.0.8: {}

  ansi-regex@2.1.1: {}

  ansi-regex@3.0.1: {}

  ansi-regex@4.1.1: {}

  ansi-regex@5.0.1: {}

  ansi-styles@2.2.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  any-observable@0.3.0(rxjs@6.6.7):
    optionalDependencies:
      rxjs: 6.6.7

  any-promise@1.3.0: {}

  anymatch@2.0.0(supports-color@6.1.0):
    dependencies:
      micromatch: 3.1.10(supports-color@6.1.0)
      normalize-path: 2.1.1
    transitivePeerDependencies:
      - supports-color

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    optional: true

  aproba@1.2.0: {}

  arch@2.2.0: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  arr-diff@4.0.0: {}

  arr-flatten@1.1.0: {}

  arr-union@3.1.0: {}

  array-buffer-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      is-array-buffer: 3.0.4

  array-flatten@1.1.1: {}

  array-flatten@2.1.2: {}

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      is-string: 1.0.7

  array-union@1.0.2:
    dependencies:
      array-uniq: 1.0.3

  array-uniq@1.0.3: {}

  array-unique@0.3.2: {}

  array.prototype.findlastindex@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.flat@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.flatmap@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.reduce@1.0.7:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-array-method-boxes-properly: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      is-string: 1.0.7

  arraybuffer.prototype.slice@1.0.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      is-array-buffer: 3.0.4
      is-shared-array-buffer: 1.0.3

  arrify@1.0.1: {}

  asn1.js@4.10.1:
    dependencies:
      bn.js: 4.12.0
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  asn1@0.2.6:
    dependencies:
      safer-buffer: 2.1.2

  assert-plus@1.0.0: {}

  assert@1.5.1:
    dependencies:
      object.assign: 4.1.5
      util: 0.10.4

  assign-symbols@1.0.0: {}

  astral-regex@1.0.0: {}

  async-each@1.0.6: {}

  async-limiter@1.0.1: {}

  async@2.6.4:
    dependencies:
      lodash: 4.17.21

  asynckit@0.4.0: {}

  atob@2.1.2: {}

  autoprefixer@9.8.8:
    dependencies:
      browserslist: 4.24.2
      caniuse-lite: 1.0.30001676
      normalize-range: 0.1.2
      num2fraction: 1.2.2
      picocolors: 0.2.1
      postcss: 7.0.39
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.0.0

  aws-sign2@0.7.0: {}

  aws4@1.13.2: {}

  axios@0.19.2:
    dependencies:
      follow-redirects: 1.5.10
    transitivePeerDependencies:
      - supports-color

  babel-code-frame@6.26.0:
    dependencies:
      chalk: 1.1.3
      esutils: 2.0.3
      js-tokens: 3.0.2

  babel-eslint@10.1.0(eslint@4.19.1):
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.2
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
      eslint: 4.19.1
      eslint-visitor-keys: 1.3.0
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  babel-eslint@10.1.0(eslint@5.16.0):
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.2
      '@babel/traverse': 7.25.9
      '@babel/types': 7.26.0
      eslint: 5.16.0
      eslint-visitor-keys: 1.3.0
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  babel-loader@8.4.1(@babel/core@7.26.0)(webpack@4.47.0):
    dependencies:
      '@babel/core': 7.26.0
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 4.47.0

  babel-plugin-dynamic-import-node@2.3.3:
    dependencies:
      object.assign: 4.1.5

  babel-plugin-module-resolver@3.2.0:
    dependencies:
      find-babel-config: 1.2.2
      glob: 7.2.3
      pkg-up: 2.0.0
      reselect: 3.0.1
      resolve: 1.22.8

  babel-plugin-polyfill-corejs2@0.4.11(@babel/core@7.26.0):
    dependencies:
      '@babel/compat-data': 7.26.2
      '@babel/core': 7.26.0
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.26.0)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.10.6(@babel/core@7.26.0):
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.26.0)
      core-js-compat: 3.39.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.2(@babel/core@7.26.0):
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.26.0)
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  base@0.11.2:
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.1
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1

  batch@0.6.1: {}

  bcrypt-pbkdf@1.0.2:
    dependencies:
      tweetnacl: 0.14.5

  bfj@6.1.2:
    dependencies:
      bluebird: 3.7.2
      check-types: 8.0.3
      hoopy: 0.1.4
      tryer: 1.0.1

  big.js@3.2.0: {}

  big.js@5.2.2: {}

  binary-extensions@1.13.1: {}

  binary-extensions@2.3.0:
    optional: true

  bindings@1.5.0:
    dependencies:
      file-uri-to-path: 1.0.0
    optional: true

  bluebird@3.7.2: {}

  bn.js@4.12.0: {}

  bn.js@5.2.1: {}

  body-parser@1.20.3(supports-color@6.1.0):
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9(supports-color@6.1.0)
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  bonjour@3.5.0:
    dependencies:
      array-flatten: 2.1.2
      deep-equal: 1.1.2
      dns-equal: 1.0.0
      dns-txt: 2.0.2
      multicast-dns: 6.2.3
      multicast-dns-service-types: 1.1.0

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  braces@2.3.2(supports-color@6.1.0):
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2(supports-color@6.1.0)
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1
    optional: true

  brorand@1.1.0: {}

  browserify-aes@1.2.0:
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.4
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1

  browserify-cipher@1.0.1:
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3

  browserify-des@1.0.2:
    dependencies:
      cipher-base: 1.0.4
      des.js: 1.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1

  browserify-rsa@4.1.1:
    dependencies:
      bn.js: 5.2.1
      randombytes: 2.1.0
      safe-buffer: 5.2.1

  browserify-sign@4.2.3:
    dependencies:
      bn.js: 5.2.1
      browserify-rsa: 4.1.1
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.6.0
      hash-base: 3.0.4
      inherits: 2.0.4
      parse-asn1: 5.1.7
      readable-stream: 2.3.8
      safe-buffer: 5.2.1

  browserify-zlib@0.2.0:
    dependencies:
      pako: 1.0.11

  browserslist@4.24.2:
    dependencies:
      caniuse-lite: 1.0.30001676
      electron-to-chromium: 1.5.50
      node-releases: 2.0.18
      update-browserslist-db: 1.1.1(browserslist@4.24.2)

  buffer-from@1.1.2: {}

  buffer-indexof@1.1.1: {}

  buffer-xor@1.0.3: {}

  buffer@4.9.2:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0

  builtin-status-codes@3.0.0: {}

  bytes@3.1.2: {}

  cacache@10.0.4:
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      glob: 7.2.3
      graceful-fs: 4.2.11
      lru-cache: 4.1.5
      mississippi: 2.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1(bluebird@3.7.2)
      rimraf: 2.7.1
      ssri: 5.3.0
      unique-filename: 1.1.1
      y18n: 4.0.3

  cacache@12.0.4:
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      mississippi: 3.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1(bluebird@3.7.2)
      rimraf: 2.7.1
      ssri: 6.0.2
      unique-filename: 1.1.1
      y18n: 4.0.3

  cache-base@1.0.1:
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.1
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0

  cache-loader@2.0.1(webpack@4.47.0):
    dependencies:
      loader-utils: 1.4.2
      mkdirp: 0.5.6
      neo-async: 2.6.2
      normalize-path: 3.0.0
      schema-utils: 1.0.0
      webpack: 4.47.0

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  call-me-maybe@1.0.2: {}

  caller-callsite@2.0.0:
    dependencies:
      callsites: 2.0.0

  caller-path@0.1.0:
    dependencies:
      callsites: 0.2.0

  caller-path@2.0.0:
    dependencies:
      caller-callsite: 2.0.0

  callsites@0.2.0: {}

  callsites@2.0.0: {}

  callsites@3.1.0: {}

  camel-case@3.0.0:
    dependencies:
      no-case: 2.3.2
      upper-case: 1.1.3

  camelcase@5.3.1: {}

  caniuse-api@3.0.0:
    dependencies:
      browserslist: 4.24.2
      caniuse-lite: 1.0.30001676
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0

  caniuse-lite@1.0.30001676: {}

  case-sensitive-paths-webpack-plugin@2.4.0: {}

  caseless@0.12.0: {}

  chalk@1.1.3:
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chardet@0.4.2: {}

  chardet@0.7.0: {}

  check-types@8.0.3: {}

  chokidar@2.1.8(supports-color@6.1.0):
    dependencies:
      anymatch: 2.0.0(supports-color@6.1.0)
      async-each: 1.0.6
      braces: 2.3.2(supports-color@6.1.0)
      glob-parent: 3.1.0
      inherits: 2.0.4
      is-binary-path: 1.0.1
      is-glob: 4.0.3
      normalize-path: 3.0.0
      path-is-absolute: 1.0.1
      readdirp: 2.2.1(supports-color@6.1.0)
      upath: 1.2.0
    optionalDependencies:
      fsevents: 1.2.13
    transitivePeerDependencies:
      - supports-color

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3
    optional: true

  chownr@1.1.4: {}

  chrome-trace-event@1.0.4: {}

  cipher-base@1.0.4:
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1

  circular-json@0.3.3: {}

  class-utils@0.3.6:
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2

  clean-css@4.2.4:
    dependencies:
      source-map: 0.6.1

  cli-cursor@2.1.0:
    dependencies:
      restore-cursor: 2.0.0

  cli-highlight@2.1.11:
    dependencies:
      chalk: 4.1.2
      highlight.js: 10.7.3
      mz: 2.7.0
      parse5: 5.1.1
      parse5-htmlparser2-tree-adapter: 6.0.1
      yargs: 16.2.0

  cli-spinners@2.9.2: {}

  cli-truncate@0.2.1:
    dependencies:
      slice-ansi: 0.0.4
      string-width: 1.0.2

  cli-width@2.2.1: {}

  clipboardy@2.3.0:
    dependencies:
      arch: 2.2.0
      execa: 1.0.0
      is-wsl: 2.2.0

  cliui@5.0.0:
    dependencies:
      string-width: 3.1.0
      strip-ansi: 5.2.0
      wrap-ansi: 5.1.0

  cliui@6.0.0:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone@1.0.4: {}

  clone@2.1.2: {}

  co@4.6.0: {}

  coa@2.0.2:
    dependencies:
      '@types/q': 1.5.8
      chalk: 2.4.2
      q: 1.5.1

  code-point-at@1.1.0: {}

  collection-visit@1.0.0:
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color@3.2.1:
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.17.1: {}

  commander@2.19.0: {}

  commander@2.20.3: {}

  comment-parser@0.5.5: {}

  commondir@1.0.1: {}

  component-emitter@1.3.1: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.53.0

  compression@1.7.5(supports-color@6.1.0):
    dependencies:
      bytes: 3.1.2
      compressible: 2.0.18
      debug: 2.6.9(supports-color@6.1.0)
      negotiator: 0.6.4
      on-headers: 1.0.2
      safe-buffer: 5.2.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  concat-map@0.0.1: {}

  concat-stream@1.6.2:
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      typedarray: 0.0.6

  connect-history-api-fallback@1.6.0: {}

  console-browserify@1.2.0: {}

  consolidate@0.15.1(lodash@4.17.21):
    dependencies:
      bluebird: 3.7.2
    optionalDependencies:
      lodash: 4.17.21

  constants-browserify@1.0.0: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.0.6: {}

  cookie@0.7.1: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1

  copy-concurrently@1.0.5:
    dependencies:
      aproba: 1.2.0
      fs-write-stream-atomic: 1.0.10
      iferr: 0.1.5
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3

  copy-descriptor@0.1.1: {}

  copy-webpack-plugin@4.6.0:
    dependencies:
      cacache: 10.0.4
      find-cache-dir: 1.0.0
      globby: 7.1.1
      is-glob: 4.0.3
      loader-utils: 1.4.2
      minimatch: 3.1.2
      p-limit: 1.3.0
      serialize-javascript: 1.9.1

  core-js-compat@3.39.0:
    dependencies:
      browserslist: 4.24.2

  core-js@2.6.12: {}

  core-util-is@1.0.2: {}

  core-util-is@1.0.3: {}

  cosmiconfig@5.2.1:
    dependencies:
      import-fresh: 2.0.0
      is-directory: 0.3.1
      js-yaml: 3.14.1
      parse-json: 4.0.0

  create-ecdh@4.0.4:
    dependencies:
      bn.js: 4.12.0
      elliptic: 6.6.0

  create-hash@1.2.0:
    dependencies:
      cipher-base: 1.0.4
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11

  create-hmac@1.1.7:
    dependencies:
      cipher-base: 1.0.4
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11

  cross-spawn@5.1.0:
    dependencies:
      lru-cache: 4.1.5
      shebang-command: 1.2.0
      which: 1.3.1

  cross-spawn@6.0.5:
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-browserify@3.12.1:
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.3
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      hash-base: 3.0.4
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4

  css-color-names@0.0.4: {}

  css-declaration-sorter@4.0.1:
    dependencies:
      postcss: 7.0.39
      timsort: 0.3.0

  css-loader@1.0.1(webpack@4.47.0):
    dependencies:
      babel-code-frame: 6.26.0
      css-selector-tokenizer: 0.7.3
      icss-utils: 2.1.0
      loader-utils: 1.4.2
      lodash: 4.17.21
      postcss: 6.0.23
      postcss-modules-extract-imports: 1.2.1
      postcss-modules-local-by-default: 1.2.0
      postcss-modules-scope: 1.1.0
      postcss-modules-values: 1.3.0
      postcss-value-parser: 3.3.1
      source-list-map: 2.0.1
      webpack: 4.47.0

  css-select-base-adapter@0.1.1: {}

  css-select@2.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 3.4.2
      domutils: 1.7.0
      nth-check: 1.0.2

  css-select@4.3.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  css-selector-tokenizer@0.7.3:
    dependencies:
      cssesc: 3.0.0
      fastparse: 1.1.2

  css-tree@1.0.0-alpha.37:
    dependencies:
      mdn-data: 2.0.4
      source-map: 0.6.1

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  css-what@3.4.2: {}

  css-what@6.1.0: {}

  cssesc@3.0.0: {}

  cssnano-preset-default@4.0.8:
    dependencies:
      css-declaration-sorter: 4.0.1
      cssnano-util-raw-cache: 4.0.1
      postcss: 7.0.39
      postcss-calc: 7.0.5
      postcss-colormin: 4.0.3
      postcss-convert-values: 4.0.1
      postcss-discard-comments: 4.0.2
      postcss-discard-duplicates: 4.0.2
      postcss-discard-empty: 4.0.1
      postcss-discard-overridden: 4.0.1
      postcss-merge-longhand: 4.0.11
      postcss-merge-rules: 4.0.3
      postcss-minify-font-values: 4.0.2
      postcss-minify-gradients: 4.0.2
      postcss-minify-params: 4.0.2
      postcss-minify-selectors: 4.0.2
      postcss-normalize-charset: 4.0.1
      postcss-normalize-display-values: 4.0.2
      postcss-normalize-positions: 4.0.2
      postcss-normalize-repeat-style: 4.0.2
      postcss-normalize-string: 4.0.2
      postcss-normalize-timing-functions: 4.0.2
      postcss-normalize-unicode: 4.0.1
      postcss-normalize-url: 4.0.1
      postcss-normalize-whitespace: 4.0.2
      postcss-ordered-values: 4.1.2
      postcss-reduce-initial: 4.0.3
      postcss-reduce-transforms: 4.0.2
      postcss-svgo: 4.0.3
      postcss-unique-selectors: 4.0.1

  cssnano-util-get-arguments@4.0.0: {}

  cssnano-util-get-match@4.0.0: {}

  cssnano-util-raw-cache@4.0.1:
    dependencies:
      postcss: 7.0.39

  cssnano-util-same-parent@4.0.1: {}

  cssnano@4.1.11:
    dependencies:
      cosmiconfig: 5.2.1
      cssnano-preset-default: 4.0.8
      is-resolvable: 1.1.0
      postcss: 7.0.39

  csso@4.2.0:
    dependencies:
      css-tree: 1.1.3

  csstype@3.1.3: {}

  current-script-polyfill@1.0.0: {}

  cyclist@1.0.2: {}

  dashdash@1.14.1:
    dependencies:
      assert-plus: 1.0.0

  data-view-buffer@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-offset@1.0.0:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  date-fns@1.30.1: {}

  de-indent@1.0.2: {}

  debug@2.6.9(supports-color@6.1.0):
    dependencies:
      ms: 2.0.0
    optionalDependencies:
      supports-color: 6.1.0

  debug@3.1.0:
    dependencies:
      ms: 2.0.0

  debug@3.2.7(supports-color@6.1.0):
    dependencies:
      ms: 2.1.3
    optionalDependencies:
      supports-color: 6.1.0

  debug@4.3.7(supports-color@6.1.0):
    dependencies:
      ms: 2.1.3
    optionalDependencies:
      supports-color: 6.1.0

  decamelize@1.2.0: {}

  decode-uri-component@0.2.2: {}

  dedent@0.7.0: {}

  deep-equal@1.1.2:
    dependencies:
      is-arguments: 1.1.1
      is-date-object: 1.0.5
      is-regex: 1.1.4
      object-is: 1.1.6
      object-keys: 1.1.1
      regexp.prototype.flags: 1.5.3

  deep-is@0.1.4: {}

  deepmerge@1.5.2: {}

  default-gateway@4.2.0:
    dependencies:
      execa: 1.0.0
      ip-regex: 2.1.0

  default-gateway@5.0.5:
    dependencies:
      execa: 3.4.0

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  define-property@0.2.5:
    dependencies:
      is-descriptor: 0.1.7

  define-property@1.0.0:
    dependencies:
      is-descriptor: 1.0.3

  define-property@2.0.2:
    dependencies:
      is-descriptor: 1.0.3
      isobject: 3.0.1

  del@3.0.0:
    dependencies:
      globby: 6.1.0
      is-path-cwd: 1.0.0
      is-path-in-cwd: 1.0.1
      p-map: 1.2.0
      pify: 3.0.0
      rimraf: 2.7.1

  del@4.1.1:
    dependencies:
      '@types/glob': 7.2.0
      globby: 6.1.0
      is-path-cwd: 2.2.0
      is-path-in-cwd: 2.1.0
      p-map: 2.1.0
      pify: 4.0.1
      rimraf: 2.7.1

  delayed-stream@1.0.0: {}

  depd@1.1.2: {}

  depd@2.0.0: {}

  des.js@1.1.0:
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  destroy@1.2.0: {}

  detect-node@2.1.0: {}

  diffie-hellman@5.0.3:
    dependencies:
      bn.js: 4.12.0
      miller-rabin: 4.0.1
      randombytes: 2.1.0

  dir-glob@2.2.2:
    dependencies:
      path-type: 3.0.0

  dns-equal@1.0.0: {}

  dns-packet@1.3.4:
    dependencies:
      ip: 1.1.9
      safe-buffer: 5.2.1

  dns-txt@2.0.2:
    dependencies:
      buffer-indexof: 1.1.1

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-converter@0.2.0:
    dependencies:
      utila: 0.4.0

  dom-serializer@0.2.2:
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0

  dom-serializer@1.4.1:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  dom7@2.1.5:
    dependencies:
      ssr-window: 2.0.0

  domain-browser@1.2.0: {}

  domelementtype@1.3.1: {}

  domelementtype@2.3.0: {}

  domhandler@4.3.1:
    dependencies:
      domelementtype: 2.3.0

  domutils@1.7.0:
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1

  domutils@2.8.0:
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  dot-prop@5.3.0:
    dependencies:
      is-obj: 2.0.0

  dotenv-expand@5.1.0: {}

  dotenv@7.0.0: {}

  duplexer@0.1.2: {}

  duplexify@3.7.1:
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 2.3.8
      stream-shift: 1.0.3

  easy-stack@1.0.1: {}

  ecc-jsbn@0.1.2:
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  ee-first@1.1.1: {}

  ejs@2.7.4: {}

  electron-to-chromium@1.5.50: {}

  elegant-spinner@1.0.1: {}

  elliptic@6.6.0:
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1

  emoji-regex@7.0.3: {}

  emoji-regex@8.0.0: {}

  emojis-list@2.1.0: {}

  emojis-list@3.0.0: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  enhanced-resolve@4.5.0:
    dependencies:
      graceful-fs: 4.2.11
      memory-fs: 0.5.0
      tapable: 1.1.3

  entities@2.2.0: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  es-abstract@1.23.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      arraybuffer.prototype.slice: 1.0.3
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      data-view-buffer: 1.0.1
      data-view-byte-length: 1.0.1
      data-view-byte-offset: 1.0.0
      es-define-property: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.0.3
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.6
      get-intrinsic: 1.2.4
      get-symbol-description: 1.0.2
      globalthis: 1.0.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
      internal-slot: 1.0.7
      is-array-buffer: 3.0.4
      is-callable: 1.2.7
      is-data-view: 1.0.1
      is-negative-zero: 2.0.3
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      is-string: 1.0.7
      is-typed-array: 1.1.13
      is-weakref: 1.0.2
      object-inspect: 1.13.2
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.3
      safe-array-concat: 1.1.2
      safe-regex-test: 1.0.3
      string.prototype.trim: 1.2.9
      string.prototype.trimend: 1.0.8
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.2
      typed-array-byte-length: 1.0.1
      typed-array-byte-offset: 1.0.2
      typed-array-length: 1.0.6
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.15

  es-array-method-boxes-properly@1.0.0: {}

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  es-object-atoms@1.0.0:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.0.3:
    dependencies:
      get-intrinsic: 1.2.4
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.0.2:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.2.1:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  eslint-config-prettier@3.6.0(eslint@5.16.0):
    dependencies:
      eslint: 5.16.0
      get-stdin: 6.0.0

  eslint-config-standard@12.0.0(eslint-plugin-import@2.31.0(eslint@5.16.0))(eslint-plugin-node@8.0.1(eslint@5.16.0))(eslint-plugin-promise@4.3.1)(eslint-plugin-standard@4.1.0(eslint@5.16.0))(eslint@5.16.0):
    dependencies:
      eslint: 5.16.0
      eslint-plugin-import: 2.31.0(eslint@5.16.0)
      eslint-plugin-node: 8.0.1(eslint@5.16.0)
      eslint-plugin-promise: 4.3.1
      eslint-plugin-standard: 4.1.0(eslint@5.16.0)

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7(supports-color@6.1.0)
      is-core-module: 2.15.1
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  eslint-loader@2.2.1(eslint@4.19.1)(webpack@4.28.4):
    dependencies:
      eslint: 4.19.1
      loader-fs-cache: 1.0.3
      loader-utils: 1.4.2
      object-assign: 4.1.1
      object-hash: 1.3.1
      rimraf: 2.7.1
      webpack: 4.28.4

  eslint-module-utils@2.12.0(eslint-import-resolver-node@0.3.9)(eslint@5.16.0):
    dependencies:
      debug: 3.2.7(supports-color@6.1.0)
    optionalDependencies:
      eslint: 5.16.0
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-es@1.4.1(eslint@5.16.0):
    dependencies:
      eslint: 5.16.0
      eslint-utils: 1.4.3
      regexpp: 2.0.1

  eslint-plugin-import@2.31.0(eslint@5.16.0):
    dependencies:
      '@rtsao/scc': 1.1.0
      array-includes: 3.1.8
      array.prototype.findlastindex: 1.2.5
      array.prototype.flat: 1.3.2
      array.prototype.flatmap: 1.3.2
      debug: 3.2.7(supports-color@6.1.0)
      doctrine: 2.1.0
      eslint: 5.16.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.0(eslint-import-resolver-node@0.3.9)(eslint@5.16.0)
      hasown: 2.0.2
      is-core-module: 2.15.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.0
      semver: 6.3.1
      string.prototype.trimend: 1.0.8
      tsconfig-paths: 3.15.0
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-jsdoc@5.0.2(eslint@5.16.0):
    dependencies:
      comment-parser: 0.5.5
      eslint: 5.16.0
      jsdoctypeparser: 3.1.0
      lodash: 4.17.21

  eslint-plugin-node@8.0.1(eslint@5.16.0):
    dependencies:
      eslint: 5.16.0
      eslint-plugin-es: 1.4.1(eslint@5.16.0)
      eslint-utils: 1.4.3
      ignore: 5.3.2
      minimatch: 3.1.2
      resolve: 1.22.8
      semver: 5.7.2

  eslint-plugin-prettier@3.4.1(eslint-config-prettier@3.6.0(eslint@5.16.0))(eslint@5.16.0)(prettier@1.19.1):
    dependencies:
      eslint: 5.16.0
      prettier: 1.19.1
      prettier-linter-helpers: 1.0.0
    optionalDependencies:
      eslint-config-prettier: 3.6.0(eslint@5.16.0)

  eslint-plugin-promise@4.3.1: {}

  eslint-plugin-standard@4.1.0(eslint@5.16.0):
    dependencies:
      eslint: 5.16.0

  eslint-plugin-vue@4.7.1(eslint@4.19.1):
    dependencies:
      eslint: 4.19.1
      vue-eslint-parser: 2.0.3(eslint@4.19.1)
    transitivePeerDependencies:
      - supports-color
    optional: true

  eslint-plugin-vue@5.2.3(eslint@5.16.0):
    dependencies:
      eslint: 5.16.0
      vue-eslint-parser: 5.0.0(eslint@5.16.0)
    transitivePeerDependencies:
      - supports-color

  eslint-scope@3.7.3:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@4.0.3:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-utils@1.4.3:
    dependencies:
      eslint-visitor-keys: 1.3.0

  eslint-visitor-keys@1.3.0: {}

  eslint@4.19.1:
    dependencies:
      ajv: 5.5.2
      babel-code-frame: 6.26.0
      chalk: 2.4.2
      concat-stream: 1.6.2
      cross-spawn: 5.1.0
      debug: 3.2.7(supports-color@6.1.0)
      doctrine: 2.1.0
      eslint-scope: 3.7.3
      eslint-visitor-keys: 1.3.0
      espree: 3.5.4
      esquery: 1.6.0
      esutils: 2.0.3
      file-entry-cache: 2.0.0
      functional-red-black-tree: 1.0.1
      glob: 7.2.3
      globals: 11.12.0
      ignore: 3.3.10
      imurmurhash: 0.1.4
      inquirer: 3.3.0
      is-resolvable: 1.1.0
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.3.0
      lodash: 4.17.21
      minimatch: 3.1.2
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      optionator: 0.8.3
      path-is-inside: 1.0.2
      pluralize: 7.0.0
      progress: 2.0.3
      regexpp: 1.1.0
      require-uncached: 1.0.3
      semver: 5.7.2
      strip-ansi: 4.0.0
      strip-json-comments: 2.0.1
      table: 4.0.2
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  eslint@5.16.0:
    dependencies:
      '@babel/code-frame': 7.26.2
      ajv: 6.12.6
      chalk: 2.4.2
      cross-spawn: 6.0.5
      debug: 4.3.7(supports-color@6.1.0)
      doctrine: 3.0.0
      eslint-scope: 4.0.3
      eslint-utils: 1.4.3
      eslint-visitor-keys: 1.3.0
      espree: 5.0.1
      esquery: 1.6.0
      esutils: 2.0.3
      file-entry-cache: 5.0.1
      functional-red-black-tree: 1.0.1
      glob: 7.2.3
      globals: 11.12.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      inquirer: 6.5.2
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.3.0
      lodash: 4.17.21
      minimatch: 3.1.2
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      optionator: 0.8.3
      path-is-inside: 1.0.2
      progress: 2.0.3
      regexpp: 2.0.1
      semver: 5.7.2
      strip-ansi: 4.0.0
      strip-json-comments: 2.0.1
      table: 5.4.6
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@3.5.4:
    dependencies:
      acorn: 5.7.4
      acorn-jsx: 3.0.1

  espree@4.1.0:
    dependencies:
      acorn: 6.4.2
      acorn-jsx: 5.3.2(acorn@6.4.2)
      eslint-visitor-keys: 1.3.0

  espree@5.0.1:
    dependencies:
      acorn: 6.4.2
      acorn-jsx: 5.3.2(acorn@6.4.2)
      eslint-visitor-keys: 1.3.0

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  event-pubsub@4.3.0: {}

  eventemitter3@4.0.7: {}

  events@3.3.0: {}

  eventsource@2.0.2: {}

  evp_bytestokey@1.0.3:
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1

  execa@1.0.0:
    dependencies:
      cross-spawn: 6.0.5
      get-stream: 4.1.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0

  execa@3.4.0:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 5.2.0
      human-signals: 1.1.1
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      p-finally: 2.0.1
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  expand-brackets@2.1.4(supports-color@6.1.0):
    dependencies:
      debug: 2.6.9(supports-color@6.1.0)
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2(supports-color@6.1.0)
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  express@4.21.1(supports-color@6.1.0):
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3(supports-color@6.1.0)
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9(supports-color@6.1.0)
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1(supports-color@6.1.0)
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.10
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0(supports-color@6.1.0)
      serve-static: 1.16.2(supports-color@6.1.0)
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  extend-shallow@2.0.1:
    dependencies:
      is-extendable: 0.1.1

  extend-shallow@3.0.2:
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1

  extend@3.0.2: {}

  external-editor@2.2.0:
    dependencies:
      chardet: 0.4.2
      iconv-lite: 0.4.24
      tmp: 0.0.33

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  extglob@2.0.4(supports-color@6.1.0):
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4(supports-color@6.1.0)
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2(supports-color@6.1.0)
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  extsprintf@1.3.0: {}

  fast-deep-equal@1.1.0: {}

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@2.2.7:
    dependencies:
      '@mrmlnc/readdir-enhanced': 2.2.1
      '@nodelib/fs.stat': 1.1.3
      glob-parent: 3.1.0
      is-glob: 4.0.3
      merge2: 1.4.1
      micromatch: 3.1.10(supports-color@6.1.0)
    transitivePeerDependencies:
      - supports-color

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fastparse@1.1.2: {}

  faye-websocket@0.11.4:
    dependencies:
      websocket-driver: 0.7.4

  figgy-pudding@3.5.2: {}

  figures@1.7.0:
    dependencies:
      escape-string-regexp: 1.0.5
      object-assign: 4.1.1

  figures@2.0.0:
    dependencies:
      escape-string-regexp: 1.0.5

  file-entry-cache@2.0.0:
    dependencies:
      flat-cache: 1.3.4
      object-assign: 4.1.1

  file-entry-cache@5.0.1:
    dependencies:
      flat-cache: 2.0.1

  file-loader@3.0.1(webpack@4.47.0):
    dependencies:
      loader-utils: 1.4.2
      schema-utils: 1.0.0
      webpack: 4.47.0

  file-uri-to-path@1.0.0:
    optional: true

  filesize@3.6.1: {}

  fill-range@4.0.0:
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1
    optional: true

  finalhandler@1.3.1(supports-color@6.1.0):
    dependencies:
      debug: 2.6.9(supports-color@6.1.0)
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-babel-config@1.2.2:
    dependencies:
      json5: 1.0.2
      path-exists: 3.0.0

  find-cache-dir@0.1.1:
    dependencies:
      commondir: 1.0.1
      mkdirp: 0.5.6
      pkg-dir: 1.0.0

  find-cache-dir@1.0.0:
    dependencies:
      commondir: 1.0.1
      make-dir: 1.3.0
      pkg-dir: 2.0.0

  find-cache-dir@2.1.0:
    dependencies:
      commondir: 1.0.1
      make-dir: 2.1.0
      pkg-dir: 3.0.0

  find-cache-dir@3.3.2:
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0

  find-up@1.1.2:
    dependencies:
      path-exists: 2.1.0
      pinkie-promise: 2.0.1

  find-up@2.1.0:
    dependencies:
      locate-path: 2.0.0

  find-up@3.0.0:
    dependencies:
      locate-path: 3.0.0

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  flat-cache@1.3.4:
    dependencies:
      circular-json: 0.3.3
      graceful-fs: 4.2.11
      rimraf: 2.6.3
      write: 0.2.1

  flat-cache@2.0.1:
    dependencies:
      flatted: 2.0.2
      rimraf: 2.6.3
      write: 1.0.3

  flatted@2.0.2: {}

  flush-write-stream@1.1.1:
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8

  fn-name@2.0.1: {}

  follow-redirects@1.15.9(debug@4.3.7(supports-color@6.1.0)):
    optionalDependencies:
      debug: 4.3.7(supports-color@6.1.0)

  follow-redirects@1.5.10:
    dependencies:
      debug: 3.1.0
    transitivePeerDependencies:
      - supports-color

  for-each@0.3.3:
    dependencies:
      is-callable: 1.2.7

  for-in@1.0.2: {}

  forever-agent@0.6.1: {}

  form-data@2.3.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  forwarded@0.2.0: {}

  fragment-cache@0.2.1:
    dependencies:
      map-cache: 0.2.2

  fresh@0.5.2: {}

  from2@2.3.0:
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8

  fs-extra@7.0.1:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2

  fs-write-stream-atomic@1.0.10:
    dependencies:
      graceful-fs: 4.2.11
      iferr: 0.1.5
      imurmurhash: 0.1.4
      readable-stream: 2.3.8

  fs.realpath@1.0.0: {}

  fsevents@1.2.13:
    dependencies:
      bindings: 1.5.0
      nan: 2.22.0
    optional: true

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      functions-have-names: 1.2.3

  functional-red-black-tree@1.0.1: {}

  functions-have-names@1.2.3: {}

  g-status@2.0.2:
    dependencies:
      arrify: 1.0.1
      matcher: 1.1.1
      simple-git: 1.132.0
    transitivePeerDependencies:
      - supports-color

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  get-own-enumerable-property-symbols@3.0.2: {}

  get-stdin@6.0.0: {}

  get-stream@4.1.0:
    dependencies:
      pump: 3.0.2

  get-stream@5.2.0:
    dependencies:
      pump: 3.0.2

  get-symbol-description@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4

  get-value@2.0.6: {}

  getpass@0.1.7:
    dependencies:
      assert-plus: 1.0.0

  glob-parent@3.1.0:
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3
    optional: true

  glob-to-regexp@0.3.0: {}

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.0.1

  globby@6.1.0:
    dependencies:
      array-union: 1.0.2
      glob: 7.2.3
      object-assign: 4.1.1
      pify: 2.3.0
      pinkie-promise: 2.0.1

  globby@7.1.1:
    dependencies:
      array-union: 1.0.2
      dir-glob: 2.2.2
      glob: 7.2.3
      ignore: 3.3.10
      pify: 3.0.0
      slash: 1.0.0

  globby@9.2.0:
    dependencies:
      '@types/glob': 7.2.0
      array-union: 1.0.2
      dir-glob: 2.2.2
      fast-glob: 2.2.7
      glob: 7.2.3
      ignore: 4.0.6
      pify: 4.0.1
      slash: 2.0.0
    transitivePeerDependencies:
      - supports-color

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  graceful-fs@4.2.11: {}

  gzip-size@5.1.1:
    dependencies:
      duplexer: 0.1.2
      pify: 4.0.1

  handle-thing@2.0.1: {}

  har-schema@2.0.0: {}

  har-validator@5.1.5:
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  has-ansi@2.0.0:
    dependencies:
      ansi-regex: 2.1.1

  has-bigints@1.0.2: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.0.3

  has-value@0.3.1:
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0

  has-value@1.0.0:
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1

  has-values@0.1.4: {}

  has-values@1.0.0:
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0

  has@1.0.4: {}

  hash-base@3.0.4:
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1

  hash-sum@1.0.2: {}

  hash.js@1.1.7:
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hex-color-regex@1.1.0: {}

  highlight.js@10.7.3: {}

  hmac-drbg@1.0.1:
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1

  hoopy@0.1.4: {}

  hosted-git-info@2.8.9: {}

  hpack.js@2.1.6:
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3

  hsl-regex@1.0.0: {}

  hsla-regex@1.0.0: {}

  html-entities@1.4.0: {}

  html-minifier@3.5.21:
    dependencies:
      camel-case: 3.0.0
      clean-css: 4.2.4
      commander: 2.17.1
      he: 1.2.0
      param-case: 2.1.1
      relateurl: 0.2.7
      uglify-js: 3.4.10

  html-tags@2.0.0: {}

  html-webpack-plugin@3.2.0(webpack@4.47.0):
    dependencies:
      html-minifier: 3.5.21
      loader-utils: 0.2.17
      lodash: 4.17.21
      pretty-error: 2.1.2
      tapable: 1.1.3
      toposort: 1.0.7
      util.promisify: 1.0.0
      webpack: 4.47.0

  htmlparser2@6.1.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0

  http-deceiver@1.2.7: {}

  http-errors@1.6.3:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-parser-js@0.5.8: {}

  http-proxy-middleware@0.19.1(debug@4.3.7(supports-color@6.1.0))(supports-color@6.1.0):
    dependencies:
      http-proxy: 1.18.1(debug@4.3.7(supports-color@6.1.0))
      is-glob: 4.0.3
      lodash: 4.17.21
      micromatch: 3.1.10(supports-color@6.1.0)
    transitivePeerDependencies:
      - debug
      - supports-color

  http-proxy@1.18.1(debug@4.3.7(supports-color@6.1.0)):
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.9(debug@4.3.7(supports-color@6.1.0))
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  http-signature@1.2.0:
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0

  https-browserify@1.0.0: {}

  human-signals@1.1.1: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  icss-replace-symbols@1.1.0: {}

  icss-utils@2.1.0:
    dependencies:
      postcss: 6.0.23

  ieee754@1.2.1: {}

  iferr@0.1.5: {}

  ignore@3.3.10: {}

  ignore@4.0.6: {}

  ignore@5.3.2: {}

  image-size@0.5.5:
    optional: true

  import-cwd@2.1.0:
    dependencies:
      import-from: 2.1.0

  import-fresh@2.0.0:
    dependencies:
      caller-path: 2.0.0
      resolve-from: 3.0.0

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-from@2.1.0:
    dependencies:
      resolve-from: 3.0.0

  import-local@2.0.0:
    dependencies:
      pkg-dir: 3.0.0
      resolve-cwd: 2.0.0

  imurmurhash@0.1.4: {}

  indent-string@3.2.0: {}

  indexes-of@1.0.1: {}

  infer-owner@1.0.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  inquirer@3.3.0:
    dependencies:
      ansi-escapes: 3.2.0
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-width: 2.2.1
      external-editor: 2.2.0
      figures: 2.0.0
      lodash: 4.17.21
      mute-stream: 0.0.7
      run-async: 2.4.1
      rx-lite: 4.0.8
      rx-lite-aggregates: 4.0.8
      string-width: 2.1.1
      strip-ansi: 4.0.0
      through: 2.3.8

  inquirer@6.5.2:
    dependencies:
      ansi-escapes: 3.2.0
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-width: 2.2.1
      external-editor: 3.1.0
      figures: 2.0.0
      lodash: 4.17.21
      mute-stream: 0.0.7
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 2.1.1
      strip-ansi: 5.2.0
      through: 2.3.8

  internal-ip@4.3.0:
    dependencies:
      default-gateway: 4.2.0
      ipaddr.js: 1.9.1

  internal-slot@1.0.7:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.0.6

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  ip-regex@2.1.0: {}

  ip@1.1.9: {}

  ipaddr.js@1.9.1: {}

  is-absolute-url@2.1.0: {}

  is-absolute-url@3.0.3: {}

  is-accessor-descriptor@1.0.1:
    dependencies:
      hasown: 2.0.2

  is-arguments@1.1.1:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-array-buffer@3.0.4:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  is-arrayish@0.2.1: {}

  is-arrayish@0.3.2: {}

  is-bigint@1.0.4:
    dependencies:
      has-bigints: 1.0.2

  is-binary-path@1.0.1:
    dependencies:
      binary-extensions: 1.13.1

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0
    optional: true

  is-boolean-object@1.1.2:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-buffer@1.1.6: {}

  is-callable@1.2.7: {}

  is-color-stop@1.1.0:
    dependencies:
      css-color-names: 0.0.4
      hex-color-regex: 1.1.0
      hsl-regex: 1.0.0
      hsla-regex: 1.0.0
      rgb-regex: 1.0.1
      rgba-regex: 1.0.0

  is-core-module@2.15.1:
    dependencies:
      hasown: 2.0.2

  is-data-descriptor@1.0.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.1:
    dependencies:
      is-typed-array: 1.1.13

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.2

  is-descriptor@0.1.7:
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1

  is-descriptor@1.0.3:
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1

  is-directory@0.3.1: {}

  is-docker@2.2.1: {}

  is-extendable@0.1.1: {}

  is-extendable@1.0.1:
    dependencies:
      is-plain-object: 2.0.4

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@1.0.0:
    dependencies:
      number-is-nan: 1.0.1

  is-fullwidth-code-point@2.0.0: {}

  is-fullwidth-code-point@3.0.0: {}

  is-glob@3.1.0:
    dependencies:
      is-extglob: 2.1.1

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-negative-zero@2.0.3: {}

  is-number-object@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-number@3.0.0:
    dependencies:
      kind-of: 3.2.2

  is-number@7.0.0:
    optional: true

  is-obj@1.0.1: {}

  is-obj@2.0.0: {}

  is-observable@1.1.0:
    dependencies:
      symbol-observable: 1.2.0

  is-path-cwd@1.0.0: {}

  is-path-cwd@2.2.0: {}

  is-path-in-cwd@1.0.1:
    dependencies:
      is-path-inside: 1.0.1

  is-path-in-cwd@2.1.0:
    dependencies:
      is-path-inside: 2.1.0

  is-path-inside@1.0.1:
    dependencies:
      path-is-inside: 1.0.2

  is-path-inside@2.1.0:
    dependencies:
      path-is-inside: 1.0.2

  is-plain-obj@1.1.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-promise@2.2.2: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-regexp@1.0.0: {}

  is-resolvable@1.1.0: {}

  is-shared-array-buffer@1.0.3:
    dependencies:
      call-bind: 1.0.7

  is-stream@1.1.0: {}

  is-stream@2.0.1: {}

  is-string@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-symbol@1.0.4:
    dependencies:
      has-symbols: 1.0.3

  is-typed-array@1.1.13:
    dependencies:
      which-typed-array: 1.1.15

  is-typedarray@1.0.0: {}

  is-weakref@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-what@3.14.1: {}

  is-windows@1.0.2: {}

  is-wsl@1.1.0: {}

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isobject@2.1.0:
    dependencies:
      isarray: 1.0.0

  isobject@3.0.1: {}

  isstream@0.1.2: {}

  javascript-stringify@1.6.0: {}

  js-levenshtein@1.1.6: {}

  js-message@1.0.7: {}

  js-queue@2.0.2:
    dependencies:
      easy-stack: 1.0.1

  js-tokens@3.0.2: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  jsbn@0.1.1: {}

  jsdoctypeparser@3.1.0: {}

  jsesc@3.0.2: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema@0.4.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-stringify-safe@5.0.1: {}

  json5@0.5.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsonfile@4.0.0:
    optionalDependencies:
      graceful-fs: 4.2.11

  jsprim@1.4.2:
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  killable@1.0.1: {}

  kind-of@3.2.2:
    dependencies:
      is-buffer: 1.1.6

  kind-of@4.0.0:
    dependencies:
      is-buffer: 1.1.6

  kind-of@6.0.3: {}

  launch-editor-middleware@2.9.1:
    dependencies:
      launch-editor: 2.9.1

  launch-editor@2.9.1:
    dependencies:
      picocolors: 1.1.1
      shell-quote: 1.8.1

  less-loader@4.1.0(less@3.13.1)(webpack@4.47.0):
    dependencies:
      clone: 2.1.2
      less: 3.13.1
      loader-utils: 1.4.2
      pify: 3.0.0
      webpack: 4.47.0

  less@3.13.1:
    dependencies:
      copy-anything: 2.0.6
      tslib: 1.14.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      native-request: 1.1.2
      source-map: 0.6.1

  levn@0.3.0:
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2

  lines-and-columns@1.2.4: {}

  lint-staged@8.2.1:
    dependencies:
      chalk: 2.4.2
      commander: 2.20.3
      cosmiconfig: 5.2.1
      debug: 3.2.7(supports-color@6.1.0)
      dedent: 0.7.0
      del: 3.0.0
      execa: 1.0.0
      g-status: 2.0.2
      is-glob: 4.0.3
      is-windows: 1.0.2
      listr: 0.14.3
      listr-update-renderer: 0.5.0(listr@0.14.3)
      lodash: 4.17.21
      log-symbols: 2.2.0
      micromatch: 3.1.10(supports-color@6.1.0)
      npm-which: 3.0.1
      p-map: 1.2.0
      path-is-inside: 1.0.2
      pify: 3.0.0
      please-upgrade-node: 3.2.0
      staged-git-files: 1.1.2
      string-argv: 0.0.2
      stringify-object: 3.3.0
      yup: 0.27.0
    transitivePeerDependencies:
      - supports-color
      - zen-observable
      - zenObservable

  listr-silent-renderer@1.1.1: {}

  listr-update-renderer@0.5.0(listr@0.14.3):
    dependencies:
      chalk: 1.1.3
      cli-truncate: 0.2.1
      elegant-spinner: 1.0.1
      figures: 1.7.0
      indent-string: 3.2.0
      listr: 0.14.3
      log-symbols: 1.0.2
      log-update: 2.3.0
      strip-ansi: 3.0.1

  listr-verbose-renderer@0.5.0:
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      date-fns: 1.30.1
      figures: 2.0.0

  listr@0.14.3:
    dependencies:
      '@samverschueren/stream-to-observable': 0.3.1(rxjs@6.6.7)
      is-observable: 1.1.0
      is-promise: 2.2.2
      is-stream: 1.1.0
      listr-silent-renderer: 1.1.1
      listr-update-renderer: 0.5.0(listr@0.14.3)
      listr-verbose-renderer: 0.5.0
      p-map: 2.1.0
      rxjs: 6.6.7
    transitivePeerDependencies:
      - zen-observable
      - zenObservable

  loader-fs-cache@1.0.3:
    dependencies:
      find-cache-dir: 0.1.1
      mkdirp: 0.5.6

  loader-runner@2.4.0: {}

  loader-utils@0.2.17:
    dependencies:
      big.js: 3.2.0
      emojis-list: 2.1.0
      json5: 0.5.1
      object-assign: 4.1.1

  loader-utils@1.4.2:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.2

  loader-utils@2.0.4:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  locate-path@2.0.0:
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0

  locate-path@3.0.0:
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  lodash.debounce@4.0.8: {}

  lodash.defaultsdeep@4.6.1: {}

  lodash.kebabcase@4.1.1: {}

  lodash.mapvalues@4.6.0: {}

  lodash.memoize@4.1.2: {}

  lodash.transform@4.6.0: {}

  lodash.uniq@4.5.0: {}

  lodash@4.17.21: {}

  log-symbols@1.0.2:
    dependencies:
      chalk: 1.1.3

  log-symbols@2.2.0:
    dependencies:
      chalk: 2.4.2

  log-update@2.3.0:
    dependencies:
      ansi-escapes: 3.2.0
      cli-cursor: 2.1.0
      wrap-ansi: 3.0.1

  loglevel@1.9.2: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case@1.1.4: {}

  lru-cache@4.1.5:
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  make-dir@1.3.0:
    dependencies:
      pify: 3.0.0

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1

  map-cache@0.2.2: {}

  map-visit@1.0.0:
    dependencies:
      object-visit: 1.0.1

  matcher@1.1.1:
    dependencies:
      escape-string-regexp: 1.0.5

  md5.js@1.3.5:
    dependencies:
      hash-base: 3.0.4
      inherits: 2.0.4
      safe-buffer: 5.2.1

  mdn-data@2.0.14: {}

  mdn-data@2.0.4: {}

  media-typer@0.3.0: {}

  memory-fs@0.4.1:
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8

  memory-fs@0.5.0:
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8

  merge-descriptors@1.0.3: {}

  merge-source-map@1.1.0:
    dependencies:
      source-map: 0.6.1

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  methods@1.1.2: {}

  micromatch@3.1.10(supports-color@6.1.0):
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2(supports-color@6.1.0)
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4(supports-color@6.1.0)
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13(supports-color@6.1.0)
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2(supports-color@6.1.0)
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  miller-rabin@4.0.1:
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0

  mime-db@1.52.0: {}

  mime-db@1.53.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mime@2.6.0: {}

  mimic-fn@1.2.0: {}

  mimic-fn@2.1.0: {}

  mini-css-extract-plugin@0.8.2(webpack@4.47.0):
    dependencies:
      loader-utils: 1.4.2
      normalize-url: 1.9.1
      schema-utils: 1.0.0
      webpack: 4.47.0
      webpack-sources: 1.4.3

  minimalistic-assert@1.0.1: {}

  minimalistic-crypto-utils@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimist@1.2.8: {}

  mississippi@2.0.0:
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.4
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 2.0.1
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5

  mississippi@3.0.0:
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.4
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 3.0.2
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5

  mixin-deep@1.3.2:
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  move-concurrently@1.0.1:
    dependencies:
      aproba: 1.2.0
      copy-concurrently: 1.0.5
      fs-write-stream-atomic: 1.0.10
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3

  ms@2.0.0: {}

  ms@2.1.3: {}

  multicast-dns-service-types@1.1.0: {}

  multicast-dns@6.2.3:
    dependencies:
      dns-packet: 1.3.4
      thunky: 1.1.0

  mute-stream@0.0.7: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nan@2.22.0:
    optional: true

  nanoid@3.3.7: {}

  nanomatch@1.2.13(supports-color@6.1.0):
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2(supports-color@6.1.0)
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  native-request@1.1.2:
    optional: true

  natural-compare@1.4.0: {}

  negotiator@0.6.3: {}

  negotiator@0.6.4: {}

  neo-async@2.6.2: {}

  nice-try@1.0.5: {}

  no-case@2.3.2:
    dependencies:
      lower-case: 1.1.4

  node-forge@0.10.0: {}

  node-ipc@9.2.1:
    dependencies:
      event-pubsub: 4.3.0
      js-message: 1.0.7
      js-queue: 2.0.2

  node-libs-browser@2.2.1:
    dependencies:
      assert: 1.5.1
      browserify-zlib: 0.2.0
      buffer: 4.9.2
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      crypto-browserify: 3.12.1
      domain-browser: 1.2.0
      events: 3.3.0
      https-browserify: 1.0.0
      os-browserify: 0.3.0
      path-browserify: 0.0.1
      process: 0.11.10
      punycode: 1.4.1
      querystring-es3: 0.2.1
      readable-stream: 2.3.8
      stream-browserify: 2.0.2
      stream-http: 2.8.3
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.0
      url: 0.11.4
      util: 0.11.1
      vm-browserify: 1.1.2

  node-releases@2.0.18: {}

  normalize-package-data@2.5.0:
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.8
      semver: 5.7.2
      validate-npm-package-license: 3.0.4

  normalize-path@2.1.1:
    dependencies:
      remove-trailing-separator: 1.1.0

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  normalize-url@1.9.1:
    dependencies:
      object-assign: 4.1.1
      prepend-http: 1.0.4
      query-string: 4.3.4
      sort-keys: 1.1.2

  normalize-url@3.3.0: {}

  npm-path@2.0.4:
    dependencies:
      which: 1.3.1

  npm-run-path@2.0.2:
    dependencies:
      path-key: 2.0.1

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npm-which@3.0.1:
    dependencies:
      commander: 2.20.3
      npm-path: 2.0.4
      which: 1.3.1

  nth-check@1.0.2:
    dependencies:
      boolbase: 1.0.0

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  num2fraction@1.2.2: {}

  number-is-nan@1.0.1: {}

  oauth-sign@0.9.0: {}

  object-assign@4.1.1: {}

  object-copy@0.1.0:
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2

  object-hash@1.3.1: {}

  object-inspect@1.13.2: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  object-visit@1.0.1:
    dependencies:
      isobject: 3.0.1

  object.assign@4.1.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  object.getownpropertydescriptors@2.1.8:
    dependencies:
      array.prototype.reduce: 1.0.7
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0
      gopd: 1.0.1
      safe-array-concat: 1.1.2

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3

  object.pick@1.3.0:
    dependencies:
      isobject: 3.0.1

  object.values@1.2.0:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  obuf@1.1.2: {}

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@2.0.1:
    dependencies:
      mimic-fn: 1.2.0

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  open@6.4.0:
    dependencies:
      is-wsl: 1.1.0

  opener@1.5.2: {}

  opn@5.5.0:
    dependencies:
      is-wsl: 1.1.0

  optionator@0.8.3:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5

  ora@3.4.0:
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-spinners: 2.9.2
      log-symbols: 2.2.0
      strip-ansi: 5.2.0
      wcwidth: 1.0.1

  os-browserify@0.3.0: {}

  os-tmpdir@1.0.2: {}

  p-finally@1.0.0: {}

  p-finally@2.0.1: {}

  p-limit@1.3.0:
    dependencies:
      p-try: 1.0.0

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-locate@2.0.0:
    dependencies:
      p-limit: 1.3.0

  p-locate@3.0.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-map@1.2.0: {}

  p-map@2.1.0: {}

  p-retry@3.0.1:
    dependencies:
      retry: 0.12.0

  p-try@1.0.0: {}

  p-try@2.2.0: {}

  pako@1.0.11: {}

  parallel-transform@1.2.0:
    dependencies:
      cyclist: 1.0.2
      inherits: 2.0.4
      readable-stream: 2.3.8

  param-case@2.1.1:
    dependencies:
      no-case: 2.3.2

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-asn1@5.1.7:
    dependencies:
      asn1.js: 4.10.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      hash-base: 3.0.4
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.26.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse5-htmlparser2-tree-adapter@6.0.1:
    dependencies:
      parse5: 6.0.1

  parse5@5.1.1: {}

  parse5@6.0.1: {}

  parseurl@1.3.3: {}

  pascalcase@0.1.1: {}

  path-browserify@0.0.1: {}

  path-dirname@1.0.2: {}

  path-exists@2.1.0:
    dependencies:
      pinkie-promise: 2.0.1

  path-exists@3.0.0: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-is-inside@1.0.2: {}

  path-key@2.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-to-regexp@0.1.10: {}

  path-type@3.0.0:
    dependencies:
      pify: 3.0.0

  pbkdf2@3.1.2:
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11

  performance-now@2.1.0: {}

  picocolors@0.2.1: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1:
    optional: true

  pify@2.3.0: {}

  pify@3.0.0: {}

  pify@4.0.1: {}

  pinkie-promise@2.0.1:
    dependencies:
      pinkie: 2.0.4

  pinkie@2.0.4: {}

  pkg-dir@1.0.0:
    dependencies:
      find-up: 1.1.2

  pkg-dir@2.0.0:
    dependencies:
      find-up: 2.1.0

  pkg-dir@3.0.0:
    dependencies:
      find-up: 3.0.0

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  pkg-up@2.0.0:
    dependencies:
      find-up: 2.1.0

  please-upgrade-node@3.2.0:
    dependencies:
      semver-compare: 1.0.0

  pluralize@7.0.0: {}

  portfinder@1.0.32(supports-color@6.1.0):
    dependencies:
      async: 2.6.4
      debug: 3.2.7(supports-color@6.1.0)
      mkdirp: 0.5.6
    transitivePeerDependencies:
      - supports-color

  posix-character-classes@0.1.1: {}

  possible-typed-array-names@1.0.0: {}

  postcss-calc@7.0.5:
    dependencies:
      postcss: 7.0.39
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0

  postcss-colormin@4.0.3:
    dependencies:
      browserslist: 4.24.2
      color: 3.2.1
      has: 1.0.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-convert-values@4.0.1:
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-discard-comments@4.0.2:
    dependencies:
      postcss: 7.0.39

  postcss-discard-duplicates@4.0.2:
    dependencies:
      postcss: 7.0.39

  postcss-discard-empty@4.0.1:
    dependencies:
      postcss: 7.0.39

  postcss-discard-overridden@4.0.1:
    dependencies:
      postcss: 7.0.39

  postcss-load-config@2.1.2:
    dependencies:
      cosmiconfig: 5.2.1
      import-cwd: 2.1.0

  postcss-loader@3.0.0:
    dependencies:
      loader-utils: 1.4.2
      postcss: 7.0.39
      postcss-load-config: 2.1.2
      schema-utils: 1.0.0

  postcss-merge-longhand@4.0.11:
    dependencies:
      css-color-names: 0.0.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      stylehacks: 4.0.3

  postcss-merge-rules@4.0.3:
    dependencies:
      browserslist: 4.24.2
      caniuse-api: 3.0.0
      cssnano-util-same-parent: 4.0.1
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2
      vendors: 1.0.4

  postcss-minify-font-values@4.0.2:
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-minify-gradients@4.0.2:
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      is-color-stop: 1.1.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-minify-params@4.0.2:
    dependencies:
      alphanum-sort: 1.0.2
      browserslist: 4.24.2
      cssnano-util-get-arguments: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      uniqs: 2.0.0

  postcss-minify-selectors@4.0.2:
    dependencies:
      alphanum-sort: 1.0.2
      has: 1.0.4
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2

  postcss-modules-extract-imports@1.2.1:
    dependencies:
      postcss: 6.0.23

  postcss-modules-local-by-default@1.2.0:
    dependencies:
      css-selector-tokenizer: 0.7.3
      postcss: 6.0.23

  postcss-modules-scope@1.1.0:
    dependencies:
      css-selector-tokenizer: 0.7.3
      postcss: 6.0.23

  postcss-modules-values@1.3.0:
    dependencies:
      icss-replace-symbols: 1.1.0
      postcss: 6.0.23

  postcss-normalize-charset@4.0.1:
    dependencies:
      postcss: 7.0.39

  postcss-normalize-display-values@4.0.2:
    dependencies:
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-normalize-positions@4.0.2:
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      has: 1.0.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-normalize-repeat-style@4.0.2:
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-normalize-string@4.0.2:
    dependencies:
      has: 1.0.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-normalize-timing-functions@4.0.2:
    dependencies:
      cssnano-util-get-match: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-normalize-unicode@4.0.1:
    dependencies:
      browserslist: 4.24.2
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-normalize-url@4.0.1:
    dependencies:
      is-absolute-url: 2.1.0
      normalize-url: 3.3.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-normalize-whitespace@4.0.2:
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-ordered-values@4.1.2:
    dependencies:
      cssnano-util-get-arguments: 4.0.0
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-reduce-initial@4.0.3:
    dependencies:
      browserslist: 4.24.2
      caniuse-api: 3.0.0
      has: 1.0.4
      postcss: 7.0.39

  postcss-reduce-transforms@4.0.2:
    dependencies:
      cssnano-util-get-match: 4.0.0
      has: 1.0.4
      postcss: 7.0.39
      postcss-value-parser: 3.3.1

  postcss-selector-parser@3.1.2:
    dependencies:
      dot-prop: 5.3.0
      indexes-of: 1.0.1
      uniq: 1.0.1

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-svgo@4.0.3:
    dependencies:
      postcss: 7.0.39
      postcss-value-parser: 3.3.1
      svgo: 1.3.2

  postcss-unique-selectors@4.0.1:
    dependencies:
      alphanum-sort: 1.0.2
      postcss: 7.0.39
      uniqs: 2.0.0

  postcss-value-parser@3.3.1: {}

  postcss-value-parser@4.2.0: {}

  postcss@6.0.23:
    dependencies:
      chalk: 2.4.2
      source-map: 0.6.1
      supports-color: 5.5.0

  postcss@7.0.39:
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1

  postcss@8.4.47:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.1.2: {}

  prepend-http@1.0.4: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@1.19.1: {}

  prettier@2.8.8:
    optional: true

  pretty-error@2.1.2:
    dependencies:
      lodash: 4.17.21
      renderkid: 2.0.7

  process-nextick-args@2.0.1: {}

  process@0.11.10: {}

  progress@2.0.3: {}

  promise-inflight@1.0.1(bluebird@3.7.2):
    optionalDependencies:
      bluebird: 3.7.2

  property-expr@1.5.1: {}

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  prr@1.0.1: {}

  pseudomap@1.0.2: {}

  psl@1.9.0: {}

  public-encrypt@4.0.3:
    dependencies:
      bn.js: 4.12.0
      browserify-rsa: 4.1.1
      create-hash: 1.2.0
      parse-asn1: 5.1.7
      randombytes: 2.1.0
      safe-buffer: 5.2.1

  pump@2.0.1:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  pump@3.0.2:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  pumpify@1.5.1:
    dependencies:
      duplexify: 3.7.1
      inherits: 2.0.4
      pump: 2.0.1

  punycode@1.4.1: {}

  punycode@2.3.1: {}

  q@1.5.1: {}

  qs@6.13.0:
    dependencies:
      side-channel: 1.0.6

  qs@6.5.3: {}

  query-string@4.3.4:
    dependencies:
      object-assign: 4.1.1
      strict-uri-encode: 1.1.0

  querystring-es3@0.2.1: {}

  querystringify@2.2.0: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  randomfill@1.0.4:
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  read-pkg@5.2.0:
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@2.2.1(supports-color@6.1.0):
    dependencies:
      graceful-fs: 4.2.11
      micromatch: 3.1.10(supports-color@6.1.0)
      readable-stream: 2.3.8
    transitivePeerDependencies:
      - supports-color

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1
    optional: true

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.14.1: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.26.0

  regex-not@1.0.2:
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0

  regexp.prototype.flags@1.5.3:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2

  regexpp@1.1.0: {}

  regexpp@2.0.1: {}

  regexpu-core@6.1.1:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.11.2
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.8.0: {}

  regjsparser@0.11.2:
    dependencies:
      jsesc: 3.0.2

  relateurl@0.2.7: {}

  remove-trailing-separator@1.1.0: {}

  renderkid@2.0.7:
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 3.0.1

  repeat-element@1.1.4: {}

  repeat-string@1.6.1: {}

  request-promise-core@1.1.4(request@2.88.2):
    dependencies:
      lodash: 4.17.21
      request: 2.88.2

  request-promise-native@1.0.9(request@2.88.2):
    dependencies:
      request: 2.88.2
      request-promise-core: 1.1.4(request@2.88.2)
      stealthy-require: 1.1.1
      tough-cookie: 2.5.0

  request@2.88.2:
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.2
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  require-directory@2.1.1: {}

  require-main-filename@2.0.0: {}

  require-uncached@1.0.3:
    dependencies:
      caller-path: 0.1.0
      resolve-from: 1.0.1

  requires-port@1.0.0: {}

  reselect@3.0.1: {}

  resolve-cwd@2.0.0:
    dependencies:
      resolve-from: 3.0.0

  resolve-from@1.0.1: {}

  resolve-from@3.0.0: {}

  resolve-from@4.0.0: {}

  resolve-url@0.2.1: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@2.0.0:
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7

  ret@0.1.15: {}

  retry@0.12.0: {}

  rgb-regex@1.0.1: {}

  rgba-regex@1.0.0: {}

  rimraf@2.6.3:
    dependencies:
      glob: 7.2.3

  rimraf@2.7.1:
    dependencies:
      glob: 7.2.3

  ripemd160@2.0.2:
    dependencies:
      hash-base: 3.0.4
      inherits: 2.0.4

  run-async@2.4.1: {}

  run-queue@1.0.3:
    dependencies:
      aproba: 1.2.0

  rx-lite-aggregates@4.0.8:
    dependencies:
      rx-lite: 4.0.8

  rx-lite@4.0.8: {}

  rxjs@6.6.7:
    dependencies:
      tslib: 1.14.1

  safe-array-concat@1.1.2:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      isarray: 2.0.5

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-regex-test@1.0.3:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-regex: 1.1.4

  safe-regex@1.1.0:
    dependencies:
      ret: 0.1.15

  safer-buffer@2.1.2: {}

  sax@1.2.4: {}

  schema-utils@0.4.7:
    dependencies:
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@1.0.0:
    dependencies:
      ajv: 6.12.6
      ajv-errors: 1.0.1(ajv@6.12.6)
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@2.7.1:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  select-hose@2.0.0: {}

  selfsigned@1.10.14:
    dependencies:
      node-forge: 0.10.0

  semver-compare@1.0.0: {}

  semver@5.7.2: {}

  semver@6.3.1: {}

  send@0.19.0(supports-color@6.1.0):
    dependencies:
      debug: 2.6.9(supports-color@6.1.0)
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@1.9.1: {}

  serialize-javascript@4.0.0:
    dependencies:
      randombytes: 2.1.0

  serve-index@1.9.1(supports-color@6.1.0):
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9(supports-color@6.1.0)
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2(supports-color@6.1.0):
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0(supports-color@6.1.0)
    transitivePeerDependencies:
      - supports-color

  set-blocking@2.0.0: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-value@2.0.1:
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0

  setimmediate@1.0.5: {}

  setprototypeof@1.1.0: {}

  setprototypeof@1.2.0: {}

  sha.js@2.4.11:
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1

  shebang-command@1.2.0:
    dependencies:
      shebang-regex: 1.0.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@1.0.0: {}

  shebang-regex@3.0.0: {}

  shell-quote@1.8.1: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.2

  signal-exit@3.0.7: {}

  simple-git@1.132.0:
    dependencies:
      debug: 4.3.7(supports-color@6.1.0)
    transitivePeerDependencies:
      - supports-color

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  slash@1.0.0: {}

  slash@2.0.0: {}

  slice-ansi@0.0.4: {}

  slice-ansi@1.0.0:
    dependencies:
      is-fullwidth-code-point: 2.0.0

  slice-ansi@2.1.0:
    dependencies:
      ansi-styles: 3.2.1
      astral-regex: 1.0.0
      is-fullwidth-code-point: 2.0.0

  snapdragon-node@2.1.1:
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1

  snapdragon-util@3.0.1:
    dependencies:
      kind-of: 3.2.2

  snapdragon@0.8.2(supports-color@6.1.0):
    dependencies:
      base: 0.11.2
      debug: 2.6.9(supports-color@6.1.0)
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    transitivePeerDependencies:
      - supports-color

  sockjs-client@1.6.1(supports-color@6.1.0):
    dependencies:
      debug: 3.2.7(supports-color@6.1.0)
      eventsource: 2.0.2
      faye-websocket: 0.11.4
      inherits: 2.0.4
      url-parse: 1.5.10
    transitivePeerDependencies:
      - supports-color

  sockjs@0.3.24:
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4

  sort-keys@1.1.2:
    dependencies:
      is-plain-obj: 1.1.0

  source-list-map@2.0.1: {}

  source-map-js@1.2.1: {}

  source-map-resolve@0.5.3:
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map-url@0.4.1: {}

  source-map@0.5.7: {}

  source-map@0.6.1: {}

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.20

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.20

  spdx-license-ids@3.0.20: {}

  spdy-transport@3.0.0(supports-color@6.1.0):
    dependencies:
      debug: 4.3.7(supports-color@6.1.0)
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color

  spdy@4.0.2(supports-color@6.1.0):
    dependencies:
      debug: 4.3.7(supports-color@6.1.0)
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0(supports-color@6.1.0)
    transitivePeerDependencies:
      - supports-color

  split-string@3.1.0:
    dependencies:
      extend-shallow: 3.0.2

  sprintf-js@1.0.3: {}

  sshpk@1.18.0:
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  ssr-window@1.0.1: {}

  ssr-window@2.0.0: {}

  ssri@5.3.0:
    dependencies:
      safe-buffer: 5.2.1

  ssri@6.0.2:
    dependencies:
      figgy-pudding: 3.5.2

  stable@0.1.8: {}

  stackframe@1.3.4: {}

  staged-git-files@1.1.2: {}

  static-extend@0.1.2:
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  stealthy-require@1.1.1: {}

  stream-browserify@2.0.2:
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8

  stream-each@1.2.3:
    dependencies:
      end-of-stream: 1.4.4
      stream-shift: 1.0.3

  stream-http@2.8.3:
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2

  stream-shift@1.0.3: {}

  strict-uri-encode@1.1.0: {}

  string-argv@0.0.2: {}

  string-width@1.0.2:
    dependencies:
      code-point-at: 1.1.0
      is-fullwidth-code-point: 1.0.0
      strip-ansi: 3.0.1

  string-width@2.1.1:
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0

  string-width@3.1.0:
    dependencies:
      emoji-regex: 7.0.3
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 5.2.0

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string.prototype.padend@3.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.padstart@3.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trim@1.2.9:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trimend@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  stringify-object@3.3.0:
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0

  strip-ansi@3.0.1:
    dependencies:
      ansi-regex: 2.1.1

  strip-ansi@4.0.0:
    dependencies:
      ansi-regex: 3.0.1

  strip-ansi@5.2.0:
    dependencies:
      ansi-regex: 4.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-bom@3.0.0: {}

  strip-eof@1.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-json-comments@2.0.1: {}

  stylehacks@4.0.3:
    dependencies:
      browserslist: 4.24.2
      postcss: 7.0.39
      postcss-selector-parser: 3.1.2

  supports-color@2.0.0: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@6.1.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-tags@1.0.0: {}

  svgo@1.3.2:
    dependencies:
      chalk: 2.4.2
      coa: 2.0.2
      css-select: 2.1.0
      css-select-base-adapter: 0.1.1
      css-tree: 1.0.0-alpha.37
      csso: 4.2.0
      js-yaml: 3.14.1
      mkdirp: 0.5.6
      object.values: 1.2.0
      sax: 1.2.4
      stable: 0.1.8
      unquote: 1.1.1
      util.promisify: 1.0.1

  swiper@4.5.1:
    dependencies:
      dom7: 2.1.5
      ssr-window: 1.0.1

  symbol-observable@1.2.0: {}

  synchronous-promise@2.0.17: {}

  table@4.0.2:
    dependencies:
      ajv: 5.5.2
      ajv-keywords: 2.1.1(ajv@5.5.2)
      chalk: 2.4.2
      lodash: 4.17.21
      slice-ansi: 1.0.0
      string-width: 2.1.1

  table@5.4.6:
    dependencies:
      ajv: 6.12.6
      lodash: 4.17.21
      slice-ansi: 2.1.0
      string-width: 3.1.0

  tapable@1.1.3: {}

  terser-webpack-plugin@1.4.6(webpack@4.28.4):
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      is-wsl: 1.1.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.28.4
      webpack-sources: 1.4.3
      worker-farm: 1.7.0

  terser-webpack-plugin@1.4.6(webpack@4.47.0):
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      is-wsl: 1.1.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.47.0
      webpack-sources: 1.4.3
      worker-farm: 1.7.0

  terser@4.8.1:
    dependencies:
      acorn: 8.14.0
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.21

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  thread-loader@2.1.3(webpack@4.47.0):
    dependencies:
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      neo-async: 2.6.2
      webpack: 4.47.0

  through2@2.0.5:
    dependencies:
      readable-stream: 2.3.8
      xtend: 4.0.2

  through@2.3.8: {}

  thunky@1.1.0: {}

  timers-browserify@2.0.12:
    dependencies:
      setimmediate: 1.0.5

  timsort@0.3.0: {}

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  to-arraybuffer@1.0.1: {}

  to-object-path@0.3.0:
    dependencies:
      kind-of: 3.2.2

  to-regex-range@2.1.1:
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0
    optional: true

  to-regex@3.0.2:
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0

  toidentifier@1.0.1: {}

  toposort@1.0.7: {}

  toposort@2.0.2: {}

  tough-cookie@2.5.0:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1

  tryer@1.0.1: {}

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tty-browserify@0.0.0: {}

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  tweetnacl@0.14.5: {}

  type-check@0.3.2:
    dependencies:
      prelude-ls: 1.1.2

  type-fest@0.6.0: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typed-array-buffer@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-typed-array: 1.1.13

  typed-array-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-byte-offset@1.0.2:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-length@1.0.6:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
      possible-typed-array-names: 1.0.0

  typedarray@0.0.6: {}

  uglify-js@3.4.10:
    dependencies:
      commander: 2.19.0
      source-map: 0.6.1

  unbox-primitive@1.0.2:
    dependencies:
      call-bind: 1.0.7
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2

  undici-types@6.19.8: {}

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  union-value@1.0.1:
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1

  uniq@1.0.1: {}

  uniqs@2.0.0: {}

  unique-filename@1.1.1:
    dependencies:
      unique-slug: 2.0.2

  unique-slug@2.0.2:
    dependencies:
      imurmurhash: 0.1.4

  universalify@0.1.2: {}

  unpipe@1.0.0: {}

  unquote@1.1.1: {}

  unset-value@1.0.0:
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1

  upath@1.2.0: {}

  update-browserslist-db@1.1.1(browserslist@4.24.2):
    dependencies:
      browserslist: 4.24.2
      escalade: 3.2.0
      picocolors: 1.1.1

  upper-case@1.1.3: {}

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  urix@0.1.0: {}

  url-loader@1.1.2(webpack@4.47.0):
    dependencies:
      loader-utils: 1.4.2
      mime: 2.6.0
      schema-utils: 1.0.0
      webpack: 4.47.0

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  url@0.11.4:
    dependencies:
      punycode: 1.4.1
      qs: 6.13.0

  use@3.1.1: {}

  util-deprecate@1.0.2: {}

  util.promisify@1.0.0:
    dependencies:
      define-properties: 1.2.1
      object.getownpropertydescriptors: 2.1.8

  util.promisify@1.0.1:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.3
      has-symbols: 1.0.3
      object.getownpropertydescriptors: 2.1.8

  util@0.10.4:
    dependencies:
      inherits: 2.0.3

  util@0.11.1:
    dependencies:
      inherits: 2.0.3

  utila@0.4.0: {}

  utils-merge@1.0.1: {}

  uuid@3.4.0: {}

  uuid@8.3.2: {}

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  vary@1.1.2: {}

  vendors@1.0.4: {}

  verror@1.10.0:
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  vm-browserify@1.1.2: {}

  vue-awesome-swiper@3.1.3:
    dependencies:
      object-assign: 4.1.1
      swiper: 4.5.1

  vue-eslint-parser@2.0.3(eslint@4.19.1):
    dependencies:
      debug: 3.2.7(supports-color@6.1.0)
      eslint: 4.19.1
      eslint-scope: 3.7.3
      eslint-visitor-keys: 1.3.0
      espree: 3.5.4
      esquery: 1.6.0
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color
    optional: true

  vue-eslint-parser@5.0.0(eslint@5.16.0):
    dependencies:
      debug: 4.3.7(supports-color@6.1.0)
      eslint: 5.16.0
      eslint-scope: 4.0.3
      eslint-visitor-keys: 1.3.0
      espree: 4.1.0
      esquery: 1.6.0
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color

  vue-hot-reload-api@2.3.4: {}

  vue-loader@15.11.1(cache-loader@2.0.1(webpack@4.47.0))(css-loader@1.0.1(webpack@4.47.0))(lodash@4.17.21)(prettier@1.19.1)(vue-template-compiler@2.7.16)(webpack@4.47.0):
    dependencies:
      '@vue/component-compiler-utils': 3.3.0(lodash@4.17.21)
      css-loader: 1.0.1(webpack@4.47.0)
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      vue-hot-reload-api: 2.3.4
      vue-style-loader: 4.1.3
      webpack: 4.47.0
    optionalDependencies:
      cache-loader: 2.0.1(webpack@4.47.0)
      prettier: 1.19.1
      vue-template-compiler: 2.7.16
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers

  vue-router@3.0.6(vue@2.7.16):
    dependencies:
      vue: 2.7.16

  vue-style-loader@4.1.3:
    dependencies:
      hash-sum: 1.0.2
      loader-utils: 1.4.2

  vue-template-compiler@2.7.16:
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  vue-template-es2015-compiler@1.9.1: {}

  vue@2.7.16:
    dependencies:
      '@vue/compiler-sfc': 2.7.16
      csstype: 3.1.3

  watchpack-chokidar2@2.0.1:
    dependencies:
      chokidar: 2.1.8(supports-color@6.1.0)
    transitivePeerDependencies:
      - supports-color
    optional: true

  watchpack@1.7.5:
    dependencies:
      graceful-fs: 4.2.11
      neo-async: 2.6.2
    optionalDependencies:
      chokidar: 3.6.0
      watchpack-chokidar2: 2.0.1
    transitivePeerDependencies:
      - supports-color

  wbuf@1.7.3:
    dependencies:
      minimalistic-assert: 1.0.1

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  webpack-bundle-analyzer@3.9.0:
    dependencies:
      acorn: 7.4.1
      acorn-walk: 7.2.0
      bfj: 6.1.2
      chalk: 2.4.2
      commander: 2.20.3
      ejs: 2.7.4
      express: 4.21.1(supports-color@6.1.0)
      filesize: 3.6.1
      gzip-size: 5.1.1
      lodash: 4.17.21
      mkdirp: 0.5.6
      opener: 1.5.2
      ws: 6.2.3
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  webpack-chain@4.12.1:
    dependencies:
      deepmerge: 1.5.2
      javascript-stringify: 1.6.0

  webpack-dev-middleware@3.7.3(webpack@4.47.0):
    dependencies:
      memory-fs: 0.4.1
      mime: 2.6.0
      mkdirp: 0.5.6
      range-parser: 1.2.1
      webpack: 4.47.0
      webpack-log: 2.0.0

  webpack-dev-server@3.11.3(webpack@4.47.0):
    dependencies:
      ansi-html-community: 0.0.8
      bonjour: 3.5.0
      chokidar: 2.1.8(supports-color@6.1.0)
      compression: 1.7.5(supports-color@6.1.0)
      connect-history-api-fallback: 1.6.0
      debug: 4.3.7(supports-color@6.1.0)
      del: 4.1.1
      express: 4.21.1(supports-color@6.1.0)
      html-entities: 1.4.0
      http-proxy-middleware: 0.19.1(debug@4.3.7(supports-color@6.1.0))(supports-color@6.1.0)
      import-local: 2.0.0
      internal-ip: 4.3.0
      ip: 1.1.9
      is-absolute-url: 3.0.3
      killable: 1.0.1
      loglevel: 1.9.2
      opn: 5.5.0
      p-retry: 3.0.1
      portfinder: 1.0.32(supports-color@6.1.0)
      schema-utils: 1.0.0
      selfsigned: 1.10.14
      semver: 6.3.1
      serve-index: 1.9.1(supports-color@6.1.0)
      sockjs: 0.3.24
      sockjs-client: 1.6.1(supports-color@6.1.0)
      spdy: 4.0.2(supports-color@6.1.0)
      strip-ansi: 3.0.1
      supports-color: 6.1.0
      url: 0.11.4
      webpack: 4.47.0
      webpack-dev-middleware: 3.7.3(webpack@4.47.0)
      webpack-log: 2.0.0
      ws: 6.2.3
      yargs: 13.3.2
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  webpack-log@2.0.0:
    dependencies:
      ansi-colors: 3.2.4
      uuid: 3.4.0

  webpack-merge@4.2.2:
    dependencies:
      lodash: 4.17.21

  webpack-sources@1.4.3:
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1

  webpack@4.28.4:
    dependencies:
      '@webassemblyjs/ast': 1.7.11
      '@webassemblyjs/helper-module-context': 1.7.11
      '@webassemblyjs/wasm-edit': 1.7.11
      '@webassemblyjs/wasm-parser': 1.7.11
      acorn: 5.7.4
      acorn-dynamic-import: 3.0.0
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
      chrome-trace-event: 1.0.4
      enhanced-resolve: 4.5.0
      eslint-scope: 4.0.3
      json-parse-better-errors: 1.0.2
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      memory-fs: 0.4.1
      micromatch: 3.1.10(supports-color@6.1.0)
      mkdirp: 0.5.6
      neo-async: 2.6.2
      node-libs-browser: 2.2.1
      schema-utils: 0.4.7
      tapable: 1.1.3
      terser-webpack-plugin: 1.4.6(webpack@4.28.4)
      watchpack: 1.7.5
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - supports-color

  webpack@4.47.0:
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/wasm-edit': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      acorn: 6.4.2
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
      chrome-trace-event: 1.0.4
      enhanced-resolve: 4.5.0
      eslint-scope: 4.0.3
      json-parse-better-errors: 1.0.2
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      memory-fs: 0.4.1
      micromatch: 3.1.10(supports-color@6.1.0)
      mkdirp: 0.5.6
      neo-async: 2.6.2
      node-libs-browser: 2.2.1
      schema-utils: 1.0.0
      tapable: 1.1.3
      terser-webpack-plugin: 1.4.6(webpack@4.47.0)
      watchpack: 1.7.5
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - supports-color

  websocket-driver@0.7.4:
    dependencies:
      http-parser-js: 0.5.8
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4

  websocket-extensions@0.1.4: {}

  which-boxed-primitive@1.0.2:
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  which-module@2.0.1: {}

  which-typed-array@1.1.15:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.2

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  worker-farm@1.7.0:
    dependencies:
      errno: 0.1.8

  wrap-ansi@3.0.1:
    dependencies:
      string-width: 2.1.1
      strip-ansi: 4.0.0

  wrap-ansi@5.1.0:
    dependencies:
      ansi-styles: 3.2.1
      string-width: 3.1.0
      strip-ansi: 5.2.0

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrappy@1.0.2: {}

  write@0.2.1:
    dependencies:
      mkdirp: 0.5.6

  write@1.0.3:
    dependencies:
      mkdirp: 0.5.6

  ws@6.2.3:
    dependencies:
      async-limiter: 1.0.1

  xtend@4.0.2: {}

  y18n@4.0.3: {}

  y18n@5.0.8: {}

  yallist@2.1.2: {}

  yallist@3.1.1: {}

  yargs-parser@13.1.2:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs-parser@18.1.3:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs-parser@20.2.9: {}

  yargs@13.3.2:
    dependencies:
      cliui: 5.0.0
      find-up: 3.0.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 3.1.0
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 13.1.2

  yargs@15.4.1:
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yup@0.27.0:
    dependencies:
      '@babel/runtime': 7.26.0
      fn-name: 2.0.1
      lodash: 4.17.21
      property-expr: 1.5.1
      synchronous-promise: 2.0.17
      toposort: 2.0.2
