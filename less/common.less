.img-holder {
    display: block;
    position: relative;
    box-sizing: border-box;
    width: 100%;
    // height: 100%;
    overflow: hidden;
    min-height: 0;
    background: #e8e8e8 data-uri('../images/default.png') no-repeat center center;
    background-size: 58px 56px;
    > img, > a > img {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        height: 100%;
        width: 100%;
        background: none;
    }
}

a {
    -webkit-tap-highlight-color: transparent;
}

p {
    margin-top: 0;
    margin-bottom: 0;
}
