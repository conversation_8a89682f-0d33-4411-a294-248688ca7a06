{"name": "tutor-web-market-page", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^2.6.5", "swiper": "^4.0.7", "vue": "^2.6.10", "vue-awesome-swiper": "=3.1.3", "vue-router": "=3.0.6"}, "devDependencies": {"@tutor/ng-cli": "^9.0.47", "@vue/cli-plugin-babel": "^3.7.0", "@vue/cli-plugin-eslint": "=3.7.0", "@vue/cli-service": "^3.7.0", "@vue/eslint-config-prettier": "^4.0.1", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "eslint": "^5.16.0", "eslint-plugin-jsdoc": "=5.0.2", "eslint-plugin-vue": "^5.0.0", "less": "^3.0.4", "less-loader": "^4.1.0", "lint-staged": "^8.1.5", "vue-template-compiler": "^2.5.21"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}}